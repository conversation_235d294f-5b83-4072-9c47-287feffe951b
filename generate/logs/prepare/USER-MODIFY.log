INFO:__main__:Starting data extraction from MySQL
/home/<USER>/generate/prepare/mysql_extract.py:23: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, connection, params=[fecha])
INFO:__main__:Loading data to Oracle, File: /home/<USER>/output/csv/user_account_history_20250611.csv
INFO:__main__:USER-MODIFY: OK
INFO:__main__:Process completed successfull
    USER_ID  ACCOUNT_ID  ... ISSUER_OLD        GRADE_OLD
0    956621     1877212  ...        505  Normal Account 
1   3607544     4459947  ...        507  Normal Account 
2   3351335     4218880  ...        507  Normal Account 
3   3599359     4452320  ...        507  Normal Account 
4   3569738     4424735  ...        507  Normal Account 
5   3595968     4449155  ...        507  Normal Account 
6   2915885     3801304  ...        505  Normal Account 
7   3599327     4452292  ...        505  Normal Account 
8   2008949     2909702  ...        505  Normal Account 
9    957572     1878169  ...        505  Normal Account 
10  3190206     4071235  ...        505  Normal Account 
11  3604234     4456855  ...        505  Normal Account 
12   828991     1756527  ...        505  Normal Account 
13   977268     1897684  ...        505  Normal Account 
14  2832161     3719025  ...        504  Normal Account 
15  1843448     2748919  ...        504  Normal Account 
16  1003348     1923484  ...        505  Normal Account 
17  2601993     3495794  ...        504  Normal Account 
18  1870992     2775611  ...        505  Normal Account 

[19 rows x 8 columns]
19 registros insertados exitosamente.
