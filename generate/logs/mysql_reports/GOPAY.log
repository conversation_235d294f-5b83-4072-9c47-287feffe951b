Fecha convertida a formato correcto: 2025-06-10
Fecha archivo convertida a formato correcto: 20250611
Leyendo tablas -> Cabecera: SELCT COUNT(amount) AS conteo FROM conciliacion_gopay
where DATE_FORMAT(datetime,'%Y-%m-%d')= '{fecha}';
Leyendo tablas -> Cuerpo: SELECT id, fin_trx_id, ext_trx_id, datetime, trx_type, from_msisdn,
CONCAT(to_username, '@gopay'), amount FROM conciliacion_gopay
where DATE_FORMAT(datetime,'%Y-%m-%d')= '{fecha}';
Leyendo tablas -> Pie: SELECT 'TOTAL GOPAY',ROUND(COALESCE(SUM(amount),0),2) FROM conciliacion_gopay
where DATE_FORMAT(datetime,'%Y-%m-%d')= '{fecha}';
Connection opened successfully.
Error mysql: (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'SELCT COUNT(amount) AS conteo FROM conciliacion_gopay\nwhere DATE_FORMAT(datetime' at line 1")
Database connection closed.
Respuesta de la BD, count 0
Connection opened successfully.
Database connection closed.
Respuesta de la BD, count 0
Connection opened successfully.
Database connection closed.
Respuesta de la BD, count 1
cabecera None
cuerpo None
pie [{'TOTAL GOPAY': 'TOTAL GOPAY', 'ROUND(COALESCE(SUM(amount),0),2)': 0.0}]
No hay registros
BD no devolvió nada
Creando archivo.
Escribiendo archivo...
Datos del archivo /home/<USER>/output/mysql/PDP-REPORTE-GOPAY_20250611.csv
Archivo escrito. /home/<USER>/output/mysql/PDP-REPORTE-GOPAY_20250611.csv
Archivo cerrado. /home/<USER>/output/mysql/PDP-REPORTE-GOPAY_20250611.csv
Se creo archivo.
PDP-REPORTE-GOPAY_20250611.csv
