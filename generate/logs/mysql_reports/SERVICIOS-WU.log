Fecha convertida a formato correcto: 2025-06-10
Fecha archivo convertida a formato correcto: 20250611
Leyendo tablas -> Cabecera: None
Leyendo tablas -> Cuerpo: SELECT financial_id, payment_time, numero_operacion, msisdn, nro_suministro , registro, CONCAT(REPLACE(empresa, ' ', '_'),'@WU'), service_charge, total FROM Pago_servicios_wu WHERE DATE(payment_time) = '{fecha}' AND emisor = 'WU'
Leyendo tablas -> Pie: SELECT 'TOTAL WU', SUM(total) FROM Pago_servicios_wu WHERE DATE(payment_time) = '{fecha}' AND emisor = 'WU'
Connection opened successfully.
Error mysql: object of type 'NoneType' has no len()
Database connection closed.
Respuesta de la BD, count 0
Connection opened successfully.
Database connection closed.
Respuesta de la BD, count 348
Connection opened successfully.
Database connection closed.
Respuesta de la BD, count 1
cuerpo [{'financial_id': Decimal('8502506100702510691970'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 2, 50), 'numero_operacion': '5695993226387', 'msisdn': '51916739566', 'nro_suministro': '916739566', 'registro': 'PEA716C12021622025-06-1007.02.51921706630000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('71.10')}, {'financial_id': Decimal('8502506100707100502098'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 7, 9), 'numero_operacion': '5695993226467', 'msisdn': '51900893036', 'nro_suministro': '932744159', 'registro': 'PEA716C12063782025-06-1007.07.10921806650000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('7.80')}, {'financial_id': Decimal('8502506100707241332106'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 7, 23), 'numero_operacion': '5695993226472', 'msisdn': '51949841451', 'nro_suministro': '976687737', 'registro': 'PEA716C12070592025-06-1007.07.25921906670000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('46.10')}, {'financial_id': Decimal('8502506100707526712122'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 7, 52), 'numero_operacion': '5695993226471', 'msisdn': '51933313196', 'nro_suministro': '929511533', 'registro': 'PEA716C12065342025-06-1007.07.53922006690000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.20')}, {'financial_id': Decimal('1162506100708155712106'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 8, 15), 'numero_operacion': '5695993226491', 'msisdn': '51964312126', 'nro_suministro': '964312126', 'registro': 'PEA716C12073852025-06-1007.08.16922106710000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('35.00')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 12, 16), 'numero_operacion': '0', 'msisdn': '51907987526', 'nro_suministro': '970930613', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 12, 46), 'numero_operacion': '0', 'msisdn': '51907987526', 'nro_suministro': '970930613', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506100713237792217'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 13, 23), 'numero_operacion': '5695993226573', 'msisdn': '51907987526', 'nro_suministro': '970930613', 'registro': 'PEA716C12130302025-06-1007.13.24922206730000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506100715041392313'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 15, 3), 'numero_operacion': '5695993226609', 'msisdn': '51907055816', 'nro_suministro': '990733623', 'registro': 'PEA716C12142592025-06-1007.15.05922306750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('9.80')}, {'financial_id': Decimal('8502506100719463122468'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 19, 45), 'numero_operacion': '5695993226313', 'msisdn': '51927378798', 'nro_suministro': '927378798', 'registro': 'PEA716C12190142025-06-1007.19.47922406770000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('59.90')}, {'financial_id': Decimal('5902506100721487752493'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 21, 48), 'numero_operacion': '5695993226732', 'msisdn': '51930943143', 'nro_suministro': '930943143', 'registro': 'PEA716C12212362025-06-1007.21.49922506790000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('25.25')}, {'financial_id': Decimal('8502506100723516522601'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 23, 51), 'numero_operacion': '5695993226793', 'msisdn': '51907055816', 'nro_suministro': '990733623', 'registro': 'PEA716C12232572025-06-1007.23.52922606810000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('24.20')}, {'financial_id': Decimal('5902506100728423772750'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 28, 42), 'numero_operacion': '5695993226905', 'msisdn': '51933476390', 'nro_suministro': '933476390', 'registro': 'PEA716C12282142025-06-1007.28.43922706830000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.15')}, {'financial_id': Decimal('1162506100730384232870'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 30, 38), 'numero_operacion': '5695993226951', 'msisdn': '51983733585', 'nro_suministro': '981282488', 'registro': 'PEA716C12300682025-06-1007.30.39922806850000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.15')}, {'financial_id': Decimal('1162506100732562902949'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 32, 55), 'numero_operacion': '5695993227046', 'msisdn': '51988915455', 'nro_suministro': '35625697', 'registro': 'PEA716C12324362025-06-1007.32.56922906870000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('16.80')}, {'financial_id': Decimal('1162506100738363853161'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 38, 35), 'numero_operacion': '5695993227170', 'msisdn': '51984023218', 'nro_suministro': '996111991', 'registro': 'PEA716C12380252025-06-1007.38.37923006880000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('8502506100738547263141'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 38, 54), 'numero_operacion': '5695993227197', 'msisdn': '51906208138', 'nro_suministro': '906208138', 'registro': 'PEA716C12381082025-06-1007.38.55923106900000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('5902506100739189933130'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 39, 18), 'numero_operacion': '5695993227185', 'msisdn': '51957380021', 'nro_suministro': '14554978', 'registro': 'PEA716C12385672025-06-1007.39.19923206920000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('37.60')}, {'financial_id': Decimal('8502506100740510093224'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 40, 50), 'numero_operacion': '5695993227246', 'msisdn': '51957380021', 'nro_suministro': '14554978', 'registro': 'PEA716C12401602025-06-1007.40.52923306930000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('37.10')}, {'financial_id': Decimal('8502506100744411583412'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 44, 40), 'numero_operacion': '5695993227363', 'msisdn': '51928278907', 'nro_suministro': '10091023', 'registro': 'PEA716C12441852025-06-1007.44.42923406940000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('1162506100745076193424'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 45, 7), 'numero_operacion': '5695993227367', 'msisdn': '51928278907', 'nro_suministro': '10091023', 'registro': 'PEA716C12445522025-06-1007.45.08923506950000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('1162506100745404723449'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 45, 40), 'numero_operacion': '5695993227372', 'msisdn': '51928278907', 'nro_suministro': '10091023', 'registro': 'PEA716C12452302025-06-1007.45.41923606960000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('5902506100746189063438'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 46, 18), 'numero_operacion': '0', 'msisdn': '51928278907', 'nro_suministro': '10091023', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('1162506100746504083491'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 46, 50), 'numero_operacion': '5695993227459', 'msisdn': '51928278907', 'nro_suministro': '70977388', 'registro': 'PEA716C12462802025-06-1007.46.51923806980000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('1162506100747164653507'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 47, 16), 'numero_operacion': '5695993227460', 'msisdn': '51928278907', 'nro_suministro': '70977388', 'registro': 'PEA716C12470472025-06-1007.47.17923906990000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 47, 46), 'numero_operacion': '0', 'msisdn': '51928278907', 'nro_suministro': '70977388', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.00')}, {'financial_id': Decimal('1162506100753199853758'), 'payment_time': datetime.datetime(2025, 6, 10, 7, 53, 19), 'numero_operacion': '5695993227603', 'msisdn': '51930375136', 'nro_suministro': '2215990', 'registro': 'PEA716C12523632025-06-1007.53.20924007000000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('202.50')}, {'financial_id': Decimal('1162506100801406274139'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 1, 40), 'numero_operacion': '5695993227924', 'msisdn': '51966955790', 'nro_suministro': '043676911', 'registro': 'PEA716C13011832025-06-1008.01.41924107010000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.35')}, {'financial_id': Decimal('8502506100803329654260'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 3, 32), 'numero_operacion': '5695993227966', 'msisdn': '51998512238', 'nro_suministro': '922516658', 'registro': 'PEA716C13031752025-06-1008.03.33924207020000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506100805108374241'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 5, 10), 'numero_operacion': '5695993227935', 'msisdn': '51957380021', 'nro_suministro': '14557844', 'registro': 'PEA716C13022512025-06-1008.05.11924307040000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('28.50')}, {'financial_id': Decimal('8502506100805585104375'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 5, 58), 'numero_operacion': '5695993228077', 'msisdn': '51937534201', 'nro_suministro': '937476424', 'registro': 'PEA716C13050712025-06-1008.05.59924407050000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('112.13')}, {'financial_id': Decimal('8502506100810526974609'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 10, 52), 'numero_operacion': '5695993228253', 'msisdn': '51968785328', 'nro_suministro': '11130371', 'registro': 'PEA716C13100752025-06-1008.10.53924507070000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('23.50')}, {'financial_id': Decimal('8502506100812006854662'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 12), 'numero_operacion': '5695993228305', 'msisdn': '51968785328', 'nro_suministro': '11234643', 'registro': 'PEA716C13114422025-06-1008.12.01924607080000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('14.10')}, {'financial_id': Decimal('5902506100814509544699'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 14, 50), 'numero_operacion': '5695993228365', 'msisdn': '51960284499', 'nro_suministro': '960284499', 'registro': 'PEA716C13143142025-06-1008.14.51924707090000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('65.97')}, {'financial_id': Decimal('1162506100815068514788'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 15, 6), 'numero_operacion': '5695993228367', 'msisdn': '51921115612', 'nro_suministro': '921115612', 'registro': 'PEA716C13145202025-06-1008.15.07924807110000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.15')}, {'financial_id': Decimal('1162506100817306604920'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 17, 30), 'numero_operacion': '5695993228506', 'msisdn': '51948450502', 'nro_suministro': '1007834', 'registro': 'PEA716C13165592025-06-1008.17.31924907130000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('41.00')}, {'financial_id': Decimal('1162506100821338905127'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 21, 33), 'numero_operacion': '5695993228626', 'msisdn': '51995852315', 'nro_suministro': '995852315', 'registro': 'PEA716C13203962025-06-1008.21.34925007140000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('45.90')}, {'financial_id': Decimal('8502506100824042155309'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 24, 3), 'numero_operacion': '5695993228781', 'msisdn': '51973411947', 'nro_suministro': '923609024', 'registro': 'PEA716C13234232025-06-1008.24.05925107160000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('25.28')}, {'financial_id': Decimal('1162506100825054225333'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 25, 5), 'numero_operacion': '5695993228821', 'msisdn': '51970329985', 'nro_suministro': '962741150', 'registro': 'PEA716C13244402025-06-1008.25.06925207180000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('8502506100825471615406'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 25, 46), 'numero_operacion': '5695993228835', 'msisdn': '51947809637', 'nro_suministro': '947809637', 'registro': 'PEA716C13252552025-06-1008.25.47925307200000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('25.10')}, {'financial_id': Decimal('5902506100826247775370'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 26, 24), 'numero_operacion': '5695993228846', 'msisdn': '51912163590', 'nro_suministro': '912163590', 'registro': 'PEA716C13260122025-06-1008.26.25925407220000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('81.10')}, {'financial_id': Decimal('1162506100829119405578'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 29, 11), 'numero_operacion': '5695993228931', 'msisdn': '51978121556', 'nro_suministro': '17448905', 'registro': 'PEA716C13290012025-06-1008.29.12925507240000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.00')}, {'financial_id': Decimal('1162506100830392945666'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 30, 38), 'numero_operacion': '5695993229016', 'msisdn': '51910959509', 'nro_suministro': '61790067', 'registro': 'PEA716C13302382025-06-1008.30.39925607250000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('40.70')}, {'financial_id': Decimal('8502506100841584276431'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 41, 57), 'numero_operacion': '5695993229443', 'msisdn': '51922567724', 'nro_suministro': '051403568', 'registro': 'PEA716C13412322025-06-1008.41.59925707260000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('58.00')}, {'financial_id': Decimal('5902506100851221906879'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 51, 21), 'numero_operacion': '5695993229820', 'msisdn': '51981355283', 'nro_suministro': '981355283 ', 'registro': 'PEA716C13510092025-06-1008.51.22925807270000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('70.66')}, {'financial_id': Decimal('1162506100854319437054'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 54, 31), 'numero_operacion': '5695993229967', 'msisdn': '51910937976', 'nro_suministro': '2256253', 'registro': 'PEA716C13535912025-06-1008.54.32925907290000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('141.50')}, {'financial_id': Decimal('8502506100855099447227'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 55, 9), 'numero_operacion': '5695993230011', 'msisdn': '51995956534', 'nro_suministro': '65224012', 'registro': 'PEA716C13545222025-06-1008.55.10926007300000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('60.80')}, {'financial_id': Decimal('1162506100859427627391'), 'payment_time': datetime.datetime(2025, 6, 10, 8, 59, 42), 'numero_operacion': '5695993230170', 'msisdn': '51936100289', 'nro_suministro': '936100289', 'registro': 'PEA716C13592102025-06-1008.59.43926107320000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('101.10')}, {'financial_id': Decimal('1162506100905454457836'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 5, 44), 'numero_operacion': '5695993230527', 'msisdn': '51906701258', 'nro_suministro': '986811966', 'registro': 'PEA716C14051512025-06-1009.05.46926207340000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('158.75')}, {'financial_id': Decimal('8502506100907561048114'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 7, 55), 'numero_operacion': '5695993230634', 'msisdn': '51934985478', 'nro_suministro': '934985478', 'registro': 'PEA716C14073112025-06-1009.07.56926307360000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.15')}, {'financial_id': Decimal('8502506100909176598214'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 9, 17), 'numero_operacion': '5695993230704', 'msisdn': '51900829082', 'nro_suministro': '37819733', 'registro': 'PEA716C14090042025-06-1009.09.18926407380000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('852.40')}, {'financial_id': Decimal('1162506100911498478262'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 11, 49), 'numero_operacion': '5695993230818', 'msisdn': '51961136425', 'nro_suministro': '971904808', 'registro': 'PEA716C14103542025-06-1009.11.50926507390000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('748.09')}, {'financial_id': Decimal('8502506100912188388419'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 12, 18), 'numero_operacion': '5695993230886', 'msisdn': '51937024176', 'nro_suministro': '937024176', 'registro': 'PEA716C14120082025-06-1009.12.19926607410000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('24.10')}, {'financial_id': Decimal('1162506100913007718349'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 13), 'numero_operacion': '5695993230918', 'msisdn': '51968785328', 'nro_suministro': '10734388', 'registro': 'PEA716C14123962025-06-1009.13.01926707430000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.10')}, {'financial_id': Decimal('1162506100913268438384'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 13, 26), 'numero_operacion': '5695993230925', 'msisdn': '51934183700', 'nro_suministro': '943155357', 'registro': 'PEA716C14125302025-06-1009.13.27926807440000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('27.10')}, {'financial_id': Decimal('1162506100913404858407'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 13, 40), 'numero_operacion': '5695993230927', 'msisdn': '51986286893', 'nro_suministro': '989921172', 'registro': 'PEA716C14130022025-06-1009.13.41926907460000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('194.20')}, {'financial_id': Decimal('1162506100921052488978'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 21, 4), 'numero_operacion': '5695993231317', 'msisdn': '51974010656', 'nro_suministro': '26098145', 'registro': 'PEA716C14203772025-06-1009.21.06927007480000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('28.90')}, {'financial_id': Decimal('5902506100922037619009'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 22, 3), 'numero_operacion': '5695993231378', 'msisdn': '51954790725', 'nro_suministro': '72403880', 'registro': 'PEA716C14213682025-06-1009.22.04927107490000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EDPYME_ProEmpresa@WU', 'service_charge': Decimal('1.50'), 'total': Decimal('209.70')}, {'financial_id': Decimal('1162506100923284889159'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 23, 28), 'numero_operacion': '5695993231470', 'msisdn': '51980497826', 'nro_suministro': '966175052', 'registro': 'PEA716C14230762025-06-1009.23.29927207510000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('93.10')}, {'financial_id': Decimal('8502506100927013289513'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 27), 'numero_operacion': '5695993231651', 'msisdn': '51908748683', 'nro_suministro': '10920230', 'registro': 'PEA716C14264802025-06-1009.27.02927307530000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('27.00')}, {'financial_id': Decimal('8502506100930218279773'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 30, 21), 'numero_operacion': '5695993231847', 'msisdn': '51991936948', 'nro_suministro': '5329770', 'registro': 'PEA716C14294752025-06-1009.30.22927407540000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('200.30')}, {'financial_id': Decimal('5902506100933105179840'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 33, 10), 'numero_operacion': '5695993232038', 'msisdn': '51988666121', 'nro_suministro': '1779095', 'registro': 'PEA716C14321472025-06-1009.33.11927507550000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('164.11')}, {'financial_id': Decimal('8502506100934431330088'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 34, 42), 'numero_operacion': '5695993232050', 'msisdn': '51918287125', 'nro_suministro': '052603250', 'registro': 'PEA716C14340982025-06-1009.34.43927607560000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.00')}, {'financial_id': Decimal('8502506100935276030138'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 35, 27), 'numero_operacion': '5695993232219', 'msisdn': '51961956663', 'nro_suministro': '912551838', 'registro': 'PEA716C14350382025-06-1009.35.28927707570000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('17.20')}, {'financial_id': Decimal('8502506100936004320182'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 36), 'numero_operacion': '5695993232174', 'msisdn': '51921310838', 'nro_suministro': '021228567', 'registro': 'PEA716C14345332025-06-1009.36.01927807590000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('25.00')}, {'financial_id': Decimal('5902506100936082380033'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 36, 7), 'numero_operacion': '5695993232257', 'msisdn': '51981974908', 'nro_suministro': '053078079', 'registro': 'PEA716C14353112025-06-1009.36.08927907600000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('30.00')}, {'financial_id': Decimal('5902506100939108200245'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 39, 10), 'numero_operacion': '5695993232417', 'msisdn': '51950782834', 'nro_suministro': '954912692', 'registro': 'PEA716C14383132025-06-1009.39.11928007610000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('2.90')}, {'financial_id': Decimal('5902506100941054350367'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 41, 5), 'numero_operacion': '5695993232561', 'msisdn': '51967464517', 'nro_suministro': '82999860', 'registro': 'PEA716C14403872025-06-1009.41.06928107630000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.20')}, {'financial_id': Decimal('1162506100942526850606'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 42, 52), 'numero_operacion': '5695993232565', 'msisdn': '51967464517', 'nro_suministro': '70555781', 'registro': 'PEA716C14420092025-06-1009.42.53928207640000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.60')}, {'financial_id': Decimal('1162506100948260881074'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 48, 25), 'numero_operacion': '5695993232902', 'msisdn': '51904288933', 'nro_suministro': '904288933', 'registro': 'PEA716C14474572025-06-1009.48.27928307650000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('20.10')}, {'financial_id': Decimal('5902506100950275521169'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 50, 27), 'numero_operacion': '5695993233001', 'msisdn': '51988447121', 'nro_suministro': '970800702', 'registro': 'PEA716C14495762025-06-1009.50.28928407670000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.10')}, {'financial_id': Decimal('5902506100950436111198'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 50, 43), 'numero_operacion': '5695993233006', 'msisdn': '51923319911', 'nro_suministro': '923319911', 'registro': 'PEA716C14502462025-06-1009.50.44928507690000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506100951425411345'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 51, 42), 'numero_operacion': '5695993233011', 'msisdn': '51955139626', 'nro_suministro': '6924778', 'registro': 'PEA716C14512362025-06-1009.51.43928607710000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('520.40')}, {'financial_id': Decimal('5902506100951560811302'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 51, 55), 'numero_operacion': '5695993233013', 'msisdn': '51984909694', 'nro_suministro': '984909694', 'registro': 'PEA716C14512732025-06-1009.51.56928707730000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('59.90')}, {'financial_id': Decimal('5902506100954006431438'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 54), 'numero_operacion': '5695993233150', 'msisdn': '51962908588', 'nro_suministro': '17093248', 'registro': 'PEA716C14534742025-06-1009.54.01928807750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('74.20')}, {'financial_id': Decimal('1162506100954459381599'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 54, 45), 'numero_operacion': '5695993233155', 'msisdn': '51962908588', 'nro_suministro': '17093248', 'registro': 'PEA716C14543612025-06-1009.54.46928907760000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('70.40')}, {'financial_id': Decimal('1162506100955102381632'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 55, 9), 'numero_operacion': '5695993233296', 'msisdn': '51947535787', 'nro_suministro': '931345214', 'registro': 'PEA716C14544982025-06-1009.55.10929007770000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.10')}, {'financial_id': Decimal('5902506100955140431537'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 55, 13), 'numero_operacion': '5695993233154', 'msisdn': '51969762988', 'nro_suministro': '969762988', 'registro': 'PEA716C14543312025-06-1009.55.14929107790000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506100956096241721'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 56, 9), 'numero_operacion': '5695993233304', 'msisdn': '51934234920', 'nro_suministro': '5244439', 'registro': 'PEA716C14553872025-06-1009.56.10929207810000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('459.10')}, {'financial_id': Decimal('1162506100957079571791'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 57, 7), 'numero_operacion': '5695993233315', 'msisdn': '51934234920', 'nro_suministro': '5244439', 'registro': 'PEA716C14565182025-06-1009.57.08929307820000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('386.40')}, {'financial_id': Decimal('1162506100958596271939'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 58, 59), 'numero_operacion': '5695993233446', 'msisdn': '51955254042', 'nro_suministro': '0889140', 'registro': 'PEA716C14573962025-06-1009.59.00929407830000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('117.00')}, {'financial_id': Decimal('5902506100959449081904'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 59, 44), 'numero_operacion': '5695993233518', 'msisdn': '51915063925', 'nro_suministro': '10698880', 'registro': 'PEA716C14585522025-06-1009.59.45929507840000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('209.60')}, {'financial_id': Decimal('5902506100959525321917'), 'payment_time': datetime.datetime(2025, 6, 10, 9, 59, 52), 'numero_operacion': '5695993233521', 'msisdn': '51966692152', 'nro_suministro': '966692152 ', 'registro': 'PEA716C14592892025-06-1009.59.53929607850000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('28.20')}, {'financial_id': Decimal('1162506101008337732728'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 8, 33), 'numero_operacion': '5695993234026', 'msisdn': '51978113771', 'nro_suministro': '36654307', 'registro': 'PEA716C15081602025-06-1010.08.34929707870000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('82.90')}, {'financial_id': Decimal('5902506101009583442735'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 9, 58), 'numero_operacion': '5695993234030', 'msisdn': '51981543470', 'nro_suministro': '995195565', 'registro': 'PEA716C15084252025-06-1010.09.59929807880000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('155.00')}, {'financial_id': Decimal('5902506101013016463011'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 13, 1), 'numero_operacion': '5695993234188', 'msisdn': '51999481448', 'nro_suministro': '1722366', 'registro': 'PEA716C15115932025-06-1010.13.02929907900000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('277.50')}, {'financial_id': Decimal('8502506101014059383203'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 14, 5), 'numero_operacion': '5695993234358', 'msisdn': '51918373156', 'nro_suministro': '918373156', 'registro': 'PEA716C15134762025-06-1010.14.06930007910000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('61.10')}, {'financial_id': Decimal('8502506101015385193328'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 15, 38), 'numero_operacion': '5695993234496', 'msisdn': '51964853508', 'nro_suministro': '08513569', 'registro': 'PEA716C15151992025-06-1010.15.39930107930000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Alfin_Banco@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('151.20')}, {'financial_id': Decimal('1162506101023371514048'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 23, 36), 'numero_operacion': '5695993234899', 'msisdn': '51962908588', 'nro_suministro': '10956439', 'registro': 'PEA716C15232382025-06-1010.23.37930207950000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('27.80')}, {'financial_id': Decimal('5902506101028015854334'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 28, 1), 'numero_operacion': '5695993235100', 'msisdn': '51974193723', 'nro_suministro': '5151349', 'registro': 'PEA716C15274132025-06-1010.28.02930307960000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('42.90')}, {'financial_id': Decimal('5902506101033210374842'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 33, 20), 'numero_operacion': '5695993235367', 'msisdn': '51991475429', 'nro_suministro': '2709019', 'registro': 'PEA716C15323812025-06-1010.33.21930407970000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('9.50')}, {'financial_id': Decimal('8502506101035285625094'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 35, 28), 'numero_operacion': '5695993235447', 'msisdn': '51981543470', 'nro_suministro': '955944570', 'registro': 'PEA716C15345752025-06-1010.35.29930507980000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('54.10')}, {'financial_id': Decimal('1162506101036569115280'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 36, 56), 'numero_operacion': '5695993235590', 'msisdn': '51947597463', 'nro_suministro': '934649452', 'registro': 'PEA716C15363652025-06-1010.36.57930608000000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('135.85')}, {'financial_id': Decimal('8502506101037286835283'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 37, 28), 'numero_operacion': '5695993235618', 'msisdn': '51961144902', 'nro_suministro': '973388052', 'registro': 'PEA716C15371152025-06-1010.37.29930708020000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('36.10')}, {'financial_id': Decimal('8502506101043272285793'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 43, 26), 'numero_operacion': '5695993235899', 'msisdn': '51977603828', 'nro_suministro': '11038051', 'registro': 'PEA716C15430872025-06-1010.43.28930808040000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('47.30')}, {'financial_id': Decimal('5902506101048356466129'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 48, 35), 'numero_operacion': '5695993236091', 'msisdn': '51945095152', 'nro_suministro': '46964591', 'registro': 'PEA716C15472552025-06-1010.48.36930908050000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('108.20')}, {'financial_id': Decimal('5902506101050440866302'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 50, 43), 'numero_operacion': '5695993236238', 'msisdn': '51968785328', 'nro_suministro': '10715918', 'registro': 'PEA716C15503032025-06-1010.50.44931008060000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('89.20')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 51), 'numero_operacion': '0', 'msisdn': '51993688282', 'nro_suministro': '51533235', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('452.70')}, {'financial_id': Decimal('1162506101053061006651'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 53, 5), 'numero_operacion': '5695993236351', 'msisdn': '51970301832', 'nro_suministro': '1273886', 'registro': 'PEA716C15525462025-06-1010.53.06931108070000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('31.30')}, {'financial_id': Decimal('8502506101058146377020'), 'payment_time': datetime.datetime(2025, 6, 10, 10, 58, 14), 'numero_operacion': '5695993236504', 'msisdn': '51925531400', 'nro_suministro': '053399819', 'registro': 'PEA716C15574622025-06-1010.58.15931208080000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('487.03')}, {'financial_id': Decimal('1162506101102525087522'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 2, 52), 'numero_operacion': '5695993236745', 'msisdn': '51958902799', 'nro_suministro': '17270870', 'registro': 'PEA716C16023682025-06-1011.02.53931308090000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.50')}, {'financial_id': Decimal('5902506101103248417442'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 3, 24), 'numero_operacion': '5695993236759', 'msisdn': '51903501432', 'nro_suministro': '903501432', 'registro': 'PEA716C16025532025-06-1011.03.25931408100000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('49.90')}, {'financial_id': Decimal('5902506101105079637611'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 5, 7), 'numero_operacion': '5695993236829', 'msisdn': '51922567724', 'nro_suministro': '051403568', 'registro': 'PEA716C16044572025-06-1011.05.08931508120000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('35.00')}, {'financial_id': Decimal('5902506101105527527716'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 5, 52), 'numero_operacion': '5695993236711', 'msisdn': '51980048586', 'nro_suministro': '980048586 ', 'registro': 'PEA716C16051922025-06-1011.05.53931608130000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('39.90')}, {'financial_id': Decimal('1162506101115377048697'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 15, 37), 'numero_operacion': '5695993237136', 'msisdn': '51933922893', 'nro_suministro': '902730478', 'registro': 'PEA716C16152262025-06-1011.15.38931708150000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('165.90')}, {'financial_id': Decimal('1162506101115504068715'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 15, 50), 'numero_operacion': '5695993237137', 'msisdn': '51924146832', 'nro_suministro': '26600625', 'registro': 'PEA716C16152722025-06-1011.15.51931808170000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('60.00')}, {'financial_id': Decimal('1162506101116335918762'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 16, 33), 'numero_operacion': '5695993237144', 'msisdn': '51965830215', 'nro_suministro': '050493806', 'registro': 'PEA716C16160652025-06-1011.16.34931908180000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('100.00')}, {'financial_id': Decimal('1162506101117564358881'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 17, 56), 'numero_operacion': '5695993237181', 'msisdn': '51912129851', 'nro_suministro': '912857584', 'registro': 'PEA716C16174042025-06-1011.17.57932008190000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('23.20')}, {'financial_id': Decimal('5902506101118267358893'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 18, 26), 'numero_operacion': '5695993237184', 'msisdn': '51968645272', 'nro_suministro': '968645272', 'registro': 'PEA716C16174712025-06-1011.18.27932108210000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('69.90')}, {'financial_id': Decimal('8502506101122313729247'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 22, 30), 'numero_operacion': '5695993237352', 'msisdn': '51958902799', 'nro_suministro': '17247371', 'registro': 'PEA716C16221302025-06-1011.22.32932208230000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('8.70')}, {'financial_id': Decimal('5902506101125222719569'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 25, 21), 'numero_operacion': '5695993237429', 'msisdn': '51983218347', 'nro_suministro': '983218347', 'registro': 'PEA716C16242882025-06-1011.25.22932308240000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('1162506101128313169873'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 28, 30), 'numero_operacion': '5695993237535', 'msisdn': '51972947880', 'nro_suministro': '6506401', 'registro': 'PEA716C16281972025-06-1011.28.32932408260000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.20')}, {'financial_id': Decimal('8502506101130458149996'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 30, 45), 'numero_operacion': '5695993237466', 'msisdn': '51968785328', 'nro_suministro': '10732211', 'registro': 'PEA716C16303242025-06-1011.30.46932508270000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('28.90')}, {'financial_id': Decimal('8502506101133137000236'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 33, 13), 'numero_operacion': '0', 'msisdn': '51950632854', 'nro_suministro': '947835642', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('195.87')}, {'financial_id': Decimal('5902506101135515510510'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 35, 51), 'numero_operacion': '5695993237727', 'msisdn': '51921106020', 'nro_suministro': '1928634', 'registro': 'PEA716C16353092025-06-1011.35.52932708280000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('55.00')}, {'financial_id': Decimal('1162506101143160941213'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 43, 15), 'numero_operacion': '5695993237919', 'msisdn': '51915100482', 'nro_suministro': '995526011', 'registro': 'PEA716C16423092025-06-1011.43.16932808290000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506101143297771234'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 43, 29), 'numero_operacion': '5695993237933', 'msisdn': '51970329985', 'nro_suministro': '941308802', 'registro': 'PEA716C16431142025-06-1011.43.30932908310000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('63.96')}, {'financial_id': Decimal('8502506101155231282107'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 55, 22), 'numero_operacion': '5695993238233', 'msisdn': '51955129421', 'nro_suministro': '955129421', 'registro': 'PEA716C16550182025-06-1011.55.23933008330000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('33.40')}, {'financial_id': Decimal('8502506101158298072405'), 'payment_time': datetime.datetime(2025, 6, 10, 11, 58, 29), 'numero_operacion': '5695993238285', 'msisdn': '51999133902', 'nro_suministro': '15550202', 'registro': 'PEA716C16564142025-06-1011.58.30933108350000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('38.00')}, {'financial_id': Decimal('5902506101217265143984'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 17, 25), 'numero_operacion': '5695993238725', 'msisdn': '51977603828', 'nro_suministro': '11146237', 'registro': 'PEA716C17164902025-06-1012.17.27933208360000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('42.30')}, {'financial_id': Decimal('5902506101224154144634'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 24, 14), 'numero_operacion': '5695993238853', 'msisdn': '51995529633', 'nro_suministro': '995529633', 'registro': 'PEA716C17234812025-06-1012.24.16933308370000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506101225514494768'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 25, 51), 'numero_operacion': '5695993238911', 'msisdn': '51996259568', 'nro_suministro': '996259568', 'registro': 'PEA716C17252522025-06-1012.25.52933408390000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('35.10')}, {'financial_id': Decimal('5902506101230553975261'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 30, 54), 'numero_operacion': '5695993239007', 'msisdn': '51998296077', 'nro_suministro': '998296077', 'registro': 'PEA716C17301902025-06-1012.30.56933508410000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('305.90')}, {'financial_id': Decimal('1162506101234073965671'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 34, 7), 'numero_operacion': '5695993239075', 'msisdn': '51968785328', 'nro_suministro': '10701921', 'registro': 'PEA716C17334622025-06-1012.34.08933608430000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('74.90')}, {'financial_id': Decimal('5902506101234581995612'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 34, 57), 'numero_operacion': '5695993239074', 'msisdn': '51966271780', 'nro_suministro': '914611730', 'registro': 'PEA716C17333462025-06-1012.34.58933708440000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('121.01')}, {'financial_id': Decimal('5902506101236545465780'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 36, 54), 'numero_operacion': '5695993239125', 'msisdn': '51993820005', 'nro_suministro': '984233040', 'registro': 'PEA716C17362982025-06-1012.36.55933808460000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506101236579415715'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 36, 57), 'numero_operacion': '5695993239122', 'msisdn': '51933881909', 'nro_suministro': '933881909', 'registro': 'PEA716C17362352025-06-1012.36.58933908480000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506101238232546053'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 38, 22), 'numero_operacion': '5695993239135', 'msisdn': '51941110789', 'nro_suministro': '65286190', 'registro': 'PEA716C17370952025-06-1012.38.24934008500000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.90')}, {'financial_id': Decimal('8502506101239260735943'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 39, 25), 'numero_operacion': '5695993239171', 'msisdn': '51941110789', 'nro_suministro': '65286190', 'registro': 'PEA716C17385882025-06-1012.39.26934108510000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('10.20')}, {'financial_id': Decimal('1162506101240520106258'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 40, 51), 'numero_operacion': '5695993239208', 'msisdn': '51906889612', 'nro_suministro': '045573400', 'registro': 'PEA716C17401212025-06-1012.40.52934208520000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('100.00')}, {'financial_id': Decimal('1162506101241166346294'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 41, 16), 'numero_operacion': '5695993239237', 'msisdn': '51927105762', 'nro_suministro': '17878521', 'registro': 'PEA716C17404952025-06-1012.41.17934308530000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('5.50')}, {'financial_id': Decimal('5902506101243400726369'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 43, 39), 'numero_operacion': '5695993239313', 'msisdn': '51929481038', 'nro_suministro': '62094115', 'registro': 'PEA716C17432212025-06-1012.43.40934408540000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('114.30')}, {'financial_id': Decimal('5902506101249171806891'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 49, 16), 'numero_operacion': '5695993239378', 'msisdn': '51983733585', 'nro_suministro': '925732773', 'registro': 'PEA716C17481182025-06-1012.49.18934508550000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('46.10')}, {'financial_id': Decimal('1162506101249413987012'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 49, 40), 'numero_operacion': '5695993239417', 'msisdn': '51990056889', 'nro_suministro': '001139481681', 'registro': 'PEA716C17492462025-06-1012.49.42934608570000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('20.00')}, {'financial_id': Decimal('8502506101253048377108'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 53, 4), 'numero_operacion': '5695993239474', 'msisdn': '51953766004', 'nro_suministro': '949409186', 'registro': 'PEA716C17523752025-06-1012.53.05934708590000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('33.10')}, {'financial_id': Decimal('8502506101256160737367'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 56, 15), 'numero_operacion': '5695993239520', 'msisdn': '51924652421', 'nro_suministro': '1770149', 'registro': 'PEA716C17554342025-06-1012.56.16934808610000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('390.50')}, {'financial_id': Decimal('1162506101256460627605'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 56, 45), 'numero_operacion': '5695993239524', 'msisdn': '51935201786', 'nro_suministro': '1711152', 'registro': 'PEA716C17562522025-06-1012.56.46934908620000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('36.70')}, {'financial_id': Decimal('8502506101259358527657'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 59, 35), 'numero_operacion': '5695993239590', 'msisdn': '51976877366', 'nro_suministro': '927591215', 'registro': 'PEA716C17585842025-06-1012.59.36935008630000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('39.10')}, {'financial_id': Decimal('1162506101259378067883'), 'payment_time': datetime.datetime(2025, 6, 10, 12, 59, 37), 'numero_operacion': '0', 'msisdn': '51973769690', 'nro_suministro': '2509883', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('125.20')}, {'financial_id': Decimal('1162506101301215868027'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 1, 21), 'numero_operacion': '5695993239607', 'msisdn': '51963434760', 'nro_suministro': '923334095 ', 'registro': 'PEA716C18005062025-06-1013.01.22935208660000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('42.95')}, {'financial_id': Decimal('8502506101301235217803'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 1, 23), 'numero_operacion': '5695993239596', 'msisdn': '51942866863', 'nro_suministro': '942866863', 'registro': 'PEA716C17592942025-06-1013.01.24935308680000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('227.90')}, {'financial_id': Decimal('5902506101312503688855'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 12, 49), 'numero_operacion': '0', 'msisdn': '51967855524', 'nro_suministro': '914170', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('270.80')}, {'financial_id': Decimal('5902506101314322899022'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 14, 31), 'numero_operacion': '5695993239947', 'msisdn': '51967855524', 'nro_suministro': '914170', 'registro': 'PEA716C18140532025-06-1013.14.32935508710000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('270.80')}, {'financial_id': Decimal('8502506101319291219220'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 19, 28), 'numero_operacion': '5695993240104', 'msisdn': '51957021590', 'nro_suministro': '972776036', 'registro': 'PEA716C18190502025-06-1013.19.29935608720000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506101329035870375'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 29, 3), 'numero_operacion': '5695993240357', 'msisdn': '51997129617', 'nro_suministro': '05857430', 'registro': 'PEA716C18284802025-06-1013.29.04935708740000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('32.90')}, {'financial_id': Decimal('8502506101329390300164'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 29, 38), 'numero_operacion': '5695993240368', 'msisdn': '51997129617', 'nro_suministro': '18514809', 'registro': 'PEA716C18292882025-06-1013.29.39935808750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('31.20')}, {'financial_id': Decimal('1162506101332595270694'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 32, 59), 'numero_operacion': '5695993240461', 'msisdn': '51954250352', 'nro_suministro': '17738235', 'registro': 'PEA716C18324652025-06-1013.33.00935908760000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('63.30')}, {'financial_id': Decimal('8502506101339107351016'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 39, 10), 'numero_operacion': '5695993240577', 'msisdn': '51901642874', 'nro_suministro': '59365495', 'registro': 'PEA716C18371932025-06-1013.39.11936008770000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('68.00')}, {'financial_id': Decimal('8502506101344112411425'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 44, 10), 'numero_operacion': '5695993240748', 'msisdn': '51900105092', 'nro_suministro': '900105092', 'registro': 'PEA716C18434572025-06-1013.44.12936108780000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('8502506101349447851905'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 49, 44), 'numero_operacion': '5695993240901', 'msisdn': '51955587142', 'nro_suministro': '994109109', 'registro': 'PEA716C18493042025-06-1013.49.45936208800000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('166.27')}, {'financial_id': Decimal('8502506101350546162005'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 50, 54), 'numero_operacion': '5695993240930', 'msisdn': '51955587142', 'nro_suministro': '053284183', 'registro': 'PEA716C18503502025-06-1013.50.55936308820000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('151.23')}, {'financial_id': Decimal('8502506101353334852244'), 'payment_time': datetime.datetime(2025, 6, 10, 13, 53, 33), 'numero_operacion': '5695993241012', 'msisdn': '51932245641', 'nro_suministro': '932245641', 'registro': 'PEA716C18530462025-06-1013.53.34936408830000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('2.40')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 1, 4), 'numero_operacion': '0', 'msisdn': '51959543025', 'nro_suministro': '17957458', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Alfin_Banco@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('901.20')}, {'financial_id': Decimal('1162506101404044083427'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 4, 3), 'numero_operacion': '5695993241225', 'msisdn': '51906635923', 'nro_suministro': '906635923', 'registro': 'PEA716C19033862025-06-1014.04.05936508850000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('97.85')}, {'financial_id': Decimal('8502506101416498924330'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 16, 49), 'numero_operacion': '5695993241542', 'msisdn': '51945077672', 'nro_suministro': '1399716', 'registro': 'PEA716C19162652025-06-1014.16.50936608870000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('331.20')}, {'financial_id': Decimal('5902506101419203094780'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 19, 19), 'numero_operacion': '5695993241603', 'msisdn': '51943573440', 'nro_suministro': '963840290', 'registro': 'PEA716C19185562025-06-1014.19.20936708880000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('6.50')}, {'financial_id': Decimal('1162506101421403675024'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 21, 39), 'numero_operacion': '5695993241652', 'msisdn': '51933242681', 'nro_suministro': '936558891', 'registro': 'PEA716C19212252025-06-1014.21.40936808900000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('19.30')}, {'financial_id': Decimal('8502506101425251415075'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 25, 24), 'numero_operacion': '5695993241756', 'msisdn': '51968785328', 'nro_suministro': '15994405', 'registro': 'PEA716C19251072025-06-1014.25.25936908920000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('7.00')}, {'financial_id': Decimal('1162506101425450495419'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 25, 44), 'numero_operacion': '5695993241759', 'msisdn': '51910424382', 'nro_suministro': '910424382 ', 'registro': 'PEA716C19252272025-06-1014.25.45937008930000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('17.00')}, {'financial_id': Decimal('8502506101431574175661'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 31, 56), 'numero_operacion': '0', 'msisdn': '51906440115', 'nro_suministro': '906440115', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.20')}, {'financial_id': Decimal('8502506101432309745711'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 32, 30), 'numero_operacion': '0', 'msisdn': '51906440115', 'nro_suministro': '906440115', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.20')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 34, 21), 'numero_operacion': '0', 'msisdn': '51959543025', 'nro_suministro': '17957458', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Alfin_Banco@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('951.20')}, {'financial_id': Decimal('1162506101434481796216'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 34, 47), 'numero_operacion': '5695993241992', 'msisdn': '51959543025', 'nro_suministro': '17957458', 'registro': 'PEA716C19342992025-06-1014.34.48937308950000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Alfin_Banco@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('951.20')}, {'financial_id': Decimal('8502506101440070356393'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 40, 6), 'numero_operacion': '5695993242159', 'msisdn': '51995374046', 'nro_suministro': '7775958', 'registro': 'PEA716C19395482025-06-1014.40.07937408970000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('31.60')}, {'financial_id': Decimal('8502506101440346556437'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 40, 34), 'numero_operacion': '5695993242161', 'msisdn': '51943212587', 'nro_suministro': '968332366', 'registro': 'PEA716C19400012025-06-1014.40.35937508990000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506101441337046523'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 41, 33), 'numero_operacion': '5695993242177', 'msisdn': '51995374046', 'nro_suministro': '2613661', 'registro': 'PEA716C19412012025-06-1014.41.34937609010000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('33.00')}, {'financial_id': Decimal('8502506101445327996837'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 45, 32), 'numero_operacion': '5695993242279', 'msisdn': '51959543025', 'nro_suministro': '17957458', 'registro': 'PEA716C19450872025-06-1014.45.33937709020000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Alfin_Banco@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('73.30')}, {'financial_id': Decimal('1162506101448378147467'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 48, 37), 'numero_operacion': '5695993242403', 'msisdn': '51997129617', 'nro_suministro': '11320563', 'registro': 'PEA716C19481272025-06-1014.48.38937809040000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('8.40')}, {'financial_id': Decimal('5902506101450298497574'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 50, 29), 'numero_operacion': '5695993242461', 'msisdn': '51956911703', 'nro_suministro': '956911703', 'registro': 'PEA716C19501632025-06-1014.50.30937909050000000001', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('1038.95')}, {'financial_id': Decimal('1162506101457571508298'), 'payment_time': datetime.datetime(2025, 6, 10, 14, 57, 56), 'numero_operacion': '5695993242693', 'msisdn': '51988606030', 'nro_suministro': '10001807', 'registro': 'PEA716C19574232025-06-1014.57.57938009070000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electro_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('186.60')}, {'financial_id': Decimal('8502506101500238858153'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 0, 23), 'numero_operacion': '5695993242761', 'msisdn': '51997129617', 'nro_suministro': '16921216', 'registro': 'PEA716C20001242025-06-1015.00.24938109080000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('27.80')}, {'financial_id': Decimal('5902506101500284268456'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 0, 28), 'numero_operacion': '5695993242759', 'msisdn': '51988606030', 'nro_suministro': '10004311', 'registro': 'PEA716C19595522025-06-1015.00.29938209090000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('45.50')}, {'financial_id': Decimal('8502506101503162888413'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 3, 15), 'numero_operacion': '5695993242838', 'msisdn': '51969861124', 'nro_suministro': '971020083', 'registro': 'PEA716C20023882025-06-1015.03.17938309100000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.10')}, {'financial_id': Decimal('5902506101508127159146'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 8, 12), 'numero_operacion': '5695993242976', 'msisdn': '51926949447', 'nro_suministro': '10819402', 'registro': 'PEA716C20075172025-06-1015.08.13938409120000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('138.80')}, {'financial_id': Decimal('1162506101509025919348'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 9, 2), 'numero_operacion': '5695993242987', 'msisdn': '51981336261', 'nro_suministro': '941161243', 'registro': 'PEA716C20083862025-06-1015.09.03938509130000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506101509169418988'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 9, 16), 'numero_operacion': '5695993242994', 'msisdn': '51941200796', 'nro_suministro': '18849220', 'registro': 'PEA716C20090502025-06-1015.09.17938609150000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('54.60')}, {'financial_id': Decimal('5902506101510326749374'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 10, 32), 'numero_operacion': '5695993243051', 'msisdn': '51986579779', 'nro_suministro': '47802613', 'registro': 'PEA716C20101382025-06-1015.10.33938709160000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('21.90')}, {'financial_id': Decimal('8502506101515118409560'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 15, 11), 'numero_operacion': '5695993243203', 'msisdn': '51933971537', 'nro_suministro': '26568009', 'registro': 'PEA716C20144652025-06-1015.15.12938809170000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electronorte@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('178.00')}, {'financial_id': Decimal('1162506101516529910120'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 16, 52), 'numero_operacion': '5695993243254', 'msisdn': '51964919001', 'nro_suministro': '939227081', 'registro': 'PEA716C20162372025-06-1015.16.53938909180000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('24.20')}, {'financial_id': Decimal('5902506101520534300333'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 20, 53), 'numero_operacion': '5695993243421', 'msisdn': '51902956334', 'nro_suministro': '053244971', 'registro': 'PEA716C20201782025-06-1015.20.54939009200000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('701.23')}, {'financial_id': Decimal('5902506101522463370531'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 22, 45), 'numero_operacion': '5695993243480', 'msisdn': '51966869517', 'nro_suministro': '966869517', 'registro': 'PEA716C20221422025-06-1015.22.46939109210000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('34.60')}, {'financial_id': Decimal('1162506101526257321034'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 26, 25), 'numero_operacion': '5695993243596', 'msisdn': '51997063670', 'nro_suministro': '1563099', 'registro': 'PEA716C20261382025-06-1015.26.26939209230000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('90.50')}, {'financial_id': Decimal('5902506101528342301076'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 28, 33), 'numero_operacion': '5695993243661', 'msisdn': '51925365160', 'nro_suministro': '7763584', 'registro': 'PEA716C20281572025-06-1015.28.34939309240000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('33.10')}, {'financial_id': Decimal('1162506101533252031710'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 33, 24), 'numero_operacion': '5695993243806', 'msisdn': '51953168385', 'nro_suministro': '1788336', 'registro': 'PEA716C20330902025-06-1015.33.25939409250000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('36.80')}, {'financial_id': Decimal('8502506101535384431449'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 35, 38), 'numero_operacion': '5695993243899', 'msisdn': '51953168385', 'nro_suministro': '6944457', 'registro': 'PEA716C20352092025-06-1015.35.39939509260000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('59.40')}, {'financial_id': Decimal('8502506101539380211828'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 39, 37), 'numero_operacion': '0', 'msisdn': '51965490041', 'nro_suministro': '62413665', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('22.40')}, {'financial_id': Decimal('8502506101541333571998'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 41, 32), 'numero_operacion': '5695993244031', 'msisdn': '51965490041', 'nro_suministro': '62413665', 'registro': 'PEA716C20411372025-06-1015.41.34939709290000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('22.40')}, {'financial_id': Decimal('1162506101547455993009'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 47, 45), 'numero_operacion': '0', 'msisdn': '51990807213', 'nro_suministro': '16754744', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('49.00')}, {'financial_id': Decimal('1162506101548451563110'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 48, 44), 'numero_operacion': '5695993244307', 'msisdn': '51907125548', 'nro_suministro': '62141574', 'registro': 'PEA716C20483422025-06-1015.48.45939909310000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('111.30')}, {'financial_id': Decimal('8502506101549577052835'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 49, 57), 'numero_operacion': '0', 'msisdn': '51977603828', 'nro_suministro': '15216269', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('13.40')}, {'financial_id': Decimal('8502506101551023562930'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 51, 1), 'numero_operacion': '5695993244359', 'msisdn': '51907125548', 'nro_suministro': '59562350', 'registro': 'PEA716C20504262025-06-1015.51.03940109330000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('15.90')}, {'financial_id': Decimal('1162506101551297823339'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 51, 29), 'numero_operacion': '0', 'msisdn': '51990807213', 'nro_suministro': '16754744', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('49.00')}, {'financial_id': Decimal('5902506101555103973640'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 55, 10), 'numero_operacion': '0', 'msisdn': '51907125548', 'nro_suministro': '60274858', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('32.60')}, {'financial_id': Decimal('5902506101555134033645'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 55, 12), 'numero_operacion': '0', 'msisdn': '51914254227', 'nro_suministro': '60239483', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('68.00')}, {'financial_id': Decimal('1162506101559377154117'), 'payment_time': datetime.datetime(2025, 6, 10, 15, 59, 37), 'numero_operacion': '0', 'msisdn': '51914254227', 'nro_suministro': '60239483', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('68.00')}, {'financial_id': Decimal('8502506101601578823914'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 1, 57), 'numero_operacion': '0', 'msisdn': '51914254227', 'nro_suministro': '60239483', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('68.00')}, {'financial_id': Decimal('5902506101607417324834'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 7, 41), 'numero_operacion': '0', 'msisdn': '51946982054', 'nro_suministro': '906742048', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('166.62')}, {'financial_id': Decimal('1162506101608508844984'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 8, 50), 'numero_operacion': '5695993244892', 'msisdn': '51988440729', 'nro_suministro': '988440729', 'registro': 'PEA716C21082222025-06-1016.08.51940809410000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('89.52')}, {'financial_id': Decimal('8502506101609386984654'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 9, 38), 'numero_operacion': '5695993244939', 'msisdn': '51927315032', 'nro_suministro': '74297596', 'registro': 'PEA716C21092982025-06-1016.09.39940909430000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('5.10')}, {'financial_id': Decimal('5902506101611571895245'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 11, 56), 'numero_operacion': '0', 'msisdn': '51927315032', 'nro_suministro': '73740827', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('12.70')}, {'financial_id': Decimal('5902506101612445505330'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 12, 44), 'numero_operacion': '0', 'msisdn': '51927315032', 'nro_suministro': '73740827', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('12.70')}, {'financial_id': Decimal('5902506101614136225478'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 14, 13), 'numero_operacion': '5695993245099', 'msisdn': '51927315032', 'nro_suministro': '73740827', 'registro': 'PEA716C21140422025-06-1016.14.14941209460000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('12.70')}, {'financial_id': Decimal('5902506101616031575659'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 16, 2), 'numero_operacion': '0', 'msisdn': '51927315032', 'nro_suministro': '82839310', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('22.90')}, {'financial_id': Decimal('8502506101617449965463'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 17, 44), 'numero_operacion': '0', 'msisdn': '51934521106', 'nro_suministro': '934521106', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506101619421956049'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 19, 41), 'numero_operacion': '5695993245291', 'msisdn': '51927315032', 'nro_suministro': '82839310', 'registro': 'PEA716C21193272025-06-1016.19.42941509500000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('22.90')}, {'financial_id': Decimal('8502506101620154535735'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 20, 15), 'numero_operacion': '5695993245337', 'msisdn': '51927315032', 'nro_suministro': '75201366', 'registro': 'PEA716C21200712025-06-1016.20.16941609510000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.70')}, {'financial_id': Decimal('8502506101620444255788'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 20, 44), 'numero_operacion': '0', 'msisdn': '51927315032', 'nro_suministro': '73740854', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.30')}, {'financial_id': Decimal('1162506101621174546279'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 21, 17), 'numero_operacion': '5695993245360', 'msisdn': '51927315032', 'nro_suministro': '73740854', 'registro': 'PEA716C21210962025-06-1016.21.18941809530000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('6.30')}, {'financial_id': Decimal('1162506101622210156395'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 22, 20), 'numero_operacion': '5695993245404', 'msisdn': '51941200796', 'nro_suministro': '11102742', 'registro': 'PEA716C21221172025-06-1016.22.21941909540000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('12.40')}, {'financial_id': Decimal('8502506101641509087885'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 41, 50), 'numero_operacion': '5695993246051', 'msisdn': '51916937999', 'nro_suministro': '3052870', 'registro': 'PEA716C21413852025-06-1016.41.51942009550000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('13.00')}, {'financial_id': Decimal('1162506101642085268434'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 42, 8), 'numero_operacion': '0', 'msisdn': '51954360996', 'nro_suministro': '15834063', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('85.30')}, {'financial_id': Decimal('1162506101643364688599'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 43, 36), 'numero_operacion': '0', 'msisdn': '51973139558', 'nro_suministro': '10117487', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electro_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('5.80')}, {'financial_id': Decimal('5902506101651570299971'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 51, 56), 'numero_operacion': '0', 'msisdn': '51973139558', 'nro_suministro': '10127033', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electro_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('15.40')}, {'financial_id': Decimal('5902506101652012409982'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 52), 'numero_operacion': '5695993246356', 'msisdn': '51907125548', 'nro_suministro': '60274858', 'registro': 'PEA716C21514782025-06-1016.52.01942409590000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('32.60')}, {'financial_id': Decimal('1162506101654288710451'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 54, 28), 'numero_operacion': '0', 'msisdn': '51973139558', 'nro_suministro': '10127033', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electro_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('15.40')}, {'financial_id': Decimal('5902506101655545330463'), 'payment_time': datetime.datetime(2025, 6, 10, 16, 55, 54), 'numero_operacion': '5695993246456', 'msisdn': '51907125548', 'nro_suministro': '48694403', 'registro': 'PEA716C21553702025-06-1016.55.55942609610000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('252.60')}, {'financial_id': Decimal('8502506101703337920869'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 3, 33), 'numero_operacion': '0', 'msisdn': '51944914628', 'nro_suministro': '50219333', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('55.20')}, {'financial_id': Decimal('1162506101713101602364'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 13, 9), 'numero_operacion': '5695993246962', 'msisdn': '51900672275', 'nro_suministro': '61289022', 'registro': 'PEA716C22123712025-06-1017.13.10942809630000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('94.60')}, {'financial_id': Decimal('1162506101715068862544'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 15, 6), 'numero_operacion': '0', 'msisdn': '51906789826', 'nro_suministro': '8730247', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('61.70')}, {'financial_id': Decimal('5902506101725152373492'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 25, 14), 'numero_operacion': '5695993247312', 'msisdn': '51927609730', 'nro_suministro': '10174433', 'registro': 'PEA716C22244642025-06-1017.25.15943009650000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('33.70')}, {'financial_id': Decimal('5902506101729201073904'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 29, 19), 'numero_operacion': '5695993247437', 'msisdn': '51943938401', 'nro_suministro': '8517763', 'registro': 'PEA716C22284772025-06-1017.29.20943109660000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('86.30')}, {'financial_id': Decimal('8502506101730207233630'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 30, 20), 'numero_operacion': '5695993247464', 'msisdn': '51996120140', 'nro_suministro': '996120140', 'registro': 'PEA716C22295392025-06-1017.30.21943209670000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('34.60')}, {'financial_id': Decimal('5902506101732488414231'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 32, 48), 'numero_operacion': '5695993247559', 'msisdn': '51937755565', 'nro_suministro': '937755565', 'registro': 'PEA716C22323162025-06-1017.32.49943309690000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.50')}, {'financial_id': Decimal('5902506101732571134250'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 32, 56), 'numero_operacion': '5695993247523', 'msisdn': '51944843932', 'nro_suministro': '938398207', 'registro': 'PEA716C22320842025-06-1017.32.57943409710000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('48.40')}, {'financial_id': Decimal('5902506101734500634445'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 34, 49), 'numero_operacion': '5695993247601', 'msisdn': '51979602531', 'nro_suministro': '979602531', 'registro': 'PEA716C22334782025-06-1017.34.50943509730000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('28.80')}, {'financial_id': Decimal('8502506101741166254675'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 41, 16), 'numero_operacion': '5695993247766', 'msisdn': '51900199079', 'nro_suministro': '8562240', 'registro': 'PEA716C22410392025-06-1017.41.17943609750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('77.80')}, {'financial_id': Decimal('8502506101742130094772'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 42, 12), 'numero_operacion': '5695993247772', 'msisdn': '51973646355', 'nro_suministro': '973646355', 'registro': 'PEA716C22413742025-06-1017.42.13943709760000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506101744315994989'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 44, 31), 'numero_operacion': '5695993247814', 'msisdn': '51967175223', 'nro_suministro': '11022632', 'registro': 'PEA716C22442012025-06-1017.44.32943809780000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.20')}, {'financial_id': Decimal('1162506101745284465597'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 45, 28), 'numero_operacion': '5695993247864', 'msisdn': '51967175223', 'nro_suministro': '10812027', 'registro': 'PEA716C22451492025-06-1017.45.29943909790000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('56.20')}, {'financial_id': Decimal('8502506101745335595101'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 45, 33), 'numero_operacion': '5695993247862', 'msisdn': '51903101762', 'nro_suministro': '903101762', 'registro': 'PEA716C22451132025-06-1017.45.34944009800000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('39.90')}, {'financial_id': Decimal('5902506101748277145793'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 48, 27), 'numero_operacion': '5695993247932', 'msisdn': '51967175223', 'nro_suministro': '15984991', 'registro': 'PEA716C22481552025-06-1017.48.28944109820000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('90.60')}, {'financial_id': Decimal('8502506101749196065480'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 49, 19), 'numero_operacion': '5695993247962', 'msisdn': '51903006722', 'nro_suministro': '944997814', 'registro': 'PEA716C22484342025-06-1017.49.20944209830000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('132.50')}, {'financial_id': Decimal('1162506101752454876302'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 52, 45), 'numero_operacion': '5695993248041', 'msisdn': '51965490041', 'nro_suministro': '63333872', 'registro': 'PEA716C22521992025-06-1017.52.46944309850000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('34.80')}, {'financial_id': Decimal('5902506101752474446225'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 52, 47), 'numero_operacion': '5695993248045', 'msisdn': '51917362761', 'nro_suministro': '42217242', 'registro': 'PEA716C22522442025-06-1017.52.48944409860000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Alfin_Banco@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('343.90')}, {'financial_id': Decimal('1162506101754226356458'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 54, 22), 'numero_operacion': '5695993248076', 'msisdn': '51920873019', 'nro_suministro': '920873019', 'registro': 'PEA716C22534212025-06-1017.54.23944509880000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('44.70')}, {'financial_id': Decimal('8502506101758087246402'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 58, 8), 'numero_operacion': '5695993248168', 'msisdn': '51962908588', 'nro_suministro': '962909024', 'registro': 'PEA716C22575122025-06-1017.58.09944609900000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('44.10')}, {'financial_id': Decimal('8502506101759514076603'), 'payment_time': datetime.datetime(2025, 6, 10, 17, 59, 51), 'numero_operacion': '5695993248218', 'msisdn': '51968785328', 'nro_suministro': '17086468', 'registro': 'PEA716C22593502025-06-1017.59.52944709920000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('62.80')}, {'financial_id': Decimal('8502506101800575836725'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 0, 57), 'numero_operacion': '5695993248243', 'msisdn': '51945331604', 'nro_suministro': '17242400', 'registro': 'PEA716C23003672025-06-1018.00.58944809930000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.40')}, {'financial_id': Decimal('8502506101801274646770'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 1, 27), 'numero_operacion': '5695993248263', 'msisdn': '51973641545', 'nro_suministro': '973641545', 'registro': 'PEA716C23005742025-06-1018.01.28944909940000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('49.90')}, {'financial_id': Decimal('8502506101801374976785'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 1, 37), 'numero_operacion': '5695993248239', 'msisdn': '51968785328', 'nro_suministro': '17086468', 'registro': 'PEA716C23002382025-06-1018.01.38945009960000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('59.80')}, {'financial_id': Decimal('5902506101803118147293'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 3, 11), 'numero_operacion': '5695993248309', 'msisdn': '51968785328', 'nro_suministro': '11104658', 'registro': 'PEA716C23025602025-06-1018.03.12945109970000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('77.40')}, {'financial_id': Decimal('8502506101804429507096'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 4, 42), 'numero_operacion': '5695993248361', 'msisdn': '51951474141', 'nro_suministro': '10035690', 'registro': 'PEA716C23042572025-06-1018.04.43945209980000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('8.60')}, {'financial_id': Decimal('8502506101806235567259'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 6, 23), 'numero_operacion': '5695993248403', 'msisdn': '51956659433', 'nro_suministro': '953741222', 'registro': 'PEA716C23060082025-06-1018.06.24945309990000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('9.10')}, {'financial_id': Decimal('8502506101806329627276'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 6, 32), 'numero_operacion': '5695993248405', 'msisdn': '51951474141', 'nro_suministro': '10034156', 'registro': 'PEA716C23061582025-06-1018.06.33945410010000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'EMSA_Puno@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('3.10')}, {'financial_id': Decimal('8502506101806557877321'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 6, 55), 'numero_operacion': '5695993248414', 'msisdn': '51956659433', 'nro_suministro': '956659433', 'registro': 'PEA716C23063942025-06-1018.06.56945510020000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('9.10')}, {'financial_id': Decimal('1162506101807070667815'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 7, 6), 'numero_operacion': '5695993248437', 'msisdn': '51968785328', 'nro_suministro': '10733498', 'registro': 'PEA716C23064962025-06-1018.07.07945610040000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('133.70')}, {'financial_id': Decimal('8502506101808172647453'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 8, 16), 'numero_operacion': '5695993248467', 'msisdn': '51956659433', 'nro_suministro': '962228602', 'registro': 'PEA716C23080242025-06-1018.08.18945710050000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('9.10')}, {'financial_id': Decimal('8502506101808226897459'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 8, 22), 'numero_operacion': '5695993248469', 'msisdn': '51968785328', 'nro_suministro': '10733498', 'registro': 'PEA716C23080952025-06-1018.08.23945810070000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('140.60')}, {'financial_id': Decimal('8502506101810343997691'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 10, 34), 'numero_operacion': '5695993248531', 'msisdn': '51987888442', 'nro_suministro': '958424606 ', 'registro': 'PEA716C23100542025-06-1018.10.35945910080000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506101811292768219'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 11, 28), 'numero_operacion': '5695993248552', 'msisdn': '51960284499', 'nro_suministro': '918420007', 'registro': 'PEA716C23105462025-06-1018.11.29946010100000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506101812231708318'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 12, 22), 'numero_operacion': '5695993248605', 'msisdn': '51956036333', 'nro_suministro': '10730530', 'registro': 'PEA716C23121232025-06-1018.12.23946110120000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('59.90')}, {'financial_id': Decimal('1162506101812259298325'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 12, 25), 'numero_operacion': '5695993248603', 'msisdn': '51931538578', 'nro_suministro': '72312915', 'registro': 'PEA716C23121012025-06-1018.12.26946210130000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('177.80')}, {'financial_id': Decimal('8502506101813119387970'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 13, 11), 'numero_operacion': '5695993248622', 'msisdn': '51931538578', 'nro_suministro': '72237534', 'registro': 'PEA716C23130002025-06-1018.13.12946310140000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Electrocentro@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('43.60')}, {'financial_id': Decimal('5902506101815510228613'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 15, 50), 'numero_operacion': '5695993248679', 'msisdn': '51968785328', 'nro_suministro': '11305647', 'registro': 'PEA716C23153592025-06-1018.15.51946410150000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('59.80')}, {'financial_id': Decimal('1162506101816406098773'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 16, 40), 'numero_operacion': '5695993248691', 'msisdn': '51968785328', 'nro_suministro': '11305647', 'registro': 'PEA716C23162762025-06-1018.16.41946510160000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('42.30')}, {'financial_id': Decimal('8502506101819433068645'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 19, 42), 'numero_operacion': '5695993248777', 'msisdn': '51902804443', 'nro_suministro': '902804443', 'registro': 'PEA716C23191242025-06-1018.19.44946610170000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('5902506101822409209334'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 22, 40), 'numero_operacion': '5695993248826', 'msisdn': '51947541063', 'nro_suministro': '947541063', 'registro': 'PEA716C23214672025-06-1018.22.41946710190000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('37.10')}, {'financial_id': Decimal('8502506101824258889140'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 24, 25), 'numero_operacion': '5695993248880', 'msisdn': '51968785328', 'nro_suministro': '11304882', 'registro': 'PEA716C23240412025-06-1018.24.26946810210000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('250.20')}, {'financial_id': Decimal('1162506101825247709690'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 25, 24), 'numero_operacion': '5695993248829', 'msisdn': '51974535732', 'nro_suministro': '01269084', 'registro': 'PEA716C23215702025-06-1018.25.25946910220000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('1162506101826448309816'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 26, 44), 'numero_operacion': '5695993248944', 'msisdn': '51903159239', 'nro_suministro': '10696984', 'registro': 'PEA716C23262352025-06-1018.26.45947010240000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('57.30')}, {'financial_id': Decimal('8502506101828562119600'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 28, 55), 'numero_operacion': '5695993248988', 'msisdn': '51910929308', 'nro_suministro': '910929308', 'registro': 'PEA716C23283042025-06-1018.28.57947110250000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('55.23')}, {'financial_id': Decimal('8502506101831093159827'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 31, 8), 'numero_operacion': '5695993249016', 'msisdn': '51925549183', 'nro_suministro': '939562349', 'registro': 'PEA716C23295232025-06-1018.31.10947210270000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('5902506101833176030486'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 33, 17), 'numero_operacion': '5695993249054', 'msisdn': '51940870925', 'nro_suministro': '940870925', 'registro': 'PEA716C23330082025-06-1018.33.18947310290000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.20')}, {'financial_id': Decimal('5902506101837120570906'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 37, 11), 'numero_operacion': '5695993249154', 'msisdn': '51927609730', 'nro_suministro': '10041562', 'registro': 'PEA716C23361062025-06-1018.37.12947410310000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('27.80')}, {'financial_id': Decimal('1162506101838063270975'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 38, 6), 'numero_operacion': '5695993249198', 'msisdn': '51967175223', 'nro_suministro': '10811988', 'registro': 'PEA716C23375152025-06-1018.38.06947510320000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('37.60')}, {'financial_id': Decimal('8502506101839319380648'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 39, 31), 'numero_operacion': '5695993249212', 'msisdn': '51950607713', 'nro_suministro': '950607713 ', 'registro': 'PEA716C23385952025-06-1018.39.32947610330000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.30')}, {'financial_id': Decimal('1162506101842200361347'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 42, 19), 'numero_operacion': '5695993249278', 'msisdn': '51966955790', 'nro_suministro': '043676911', 'registro': 'PEA716C23415922025-06-1018.42.20947710350000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('30.00')}, {'financial_id': Decimal('5902506101843121141518'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 43, 10), 'numero_operacion': '5695993249279', 'msisdn': '51932898512', 'nro_suministro': '966197022 ', 'registro': 'PEA716C23421362025-06-1018.43.12947810360000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('69.90')}, {'financial_id': Decimal('5902506101843275271543'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 43, 27), 'numero_operacion': '5695993249289', 'msisdn': '51968785328', 'nro_suministro': '10878323', 'registro': 'PEA716C23431112025-06-1018.43.28947910380000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('95.00')}, {'financial_id': Decimal('8502506101844575111156'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 44, 57), 'numero_operacion': '5695993249276', 'msisdn': '51902138311', 'nro_suministro': '1965687', 'registro': 'PEA716C23415292025-06-1018.44.58948010390000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('77.50')}, {'financial_id': Decimal('8502506101846003991275'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 46), 'numero_operacion': '5695993249349', 'msisdn': '51927609730', 'nro_suministro': '10177186', 'registro': 'PEA716C23453442025-06-1018.46.01948110400000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('41.20')}, {'financial_id': Decimal('1162506101847009811797'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 47), 'numero_operacion': '5695993249370', 'msisdn': '51927609730', 'nro_suministro': '10177186', 'registro': 'PEA716C23463762025-06-1018.47.01948210410000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('37.40')}, {'financial_id': Decimal('5902506101847036181905'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 47, 3), 'numero_operacion': '5695993249361', 'msisdn': '51921151811', 'nro_suministro': '53243358', 'registro': 'PEA716C23461792025-06-1018.47.04948310420000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('127.00')}, {'financial_id': Decimal('1162506101849379172026'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 49, 37), 'numero_operacion': '5695993249371', 'msisdn': '51939553905', 'nro_suministro': '1051585', 'registro': 'PEA716C23464262025-06-1018.49.38948410440000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('336.91')}, {'financial_id': Decimal('5902506101850091502193'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 50, 8), 'numero_operacion': '5695993249451', 'msisdn': '51907125548', 'nro_suministro': '48548719', 'registro': 'PEA716C23495952025-06-1018.50.09948510450000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('11.50')}, {'financial_id': Decimal('8502506101850366041703'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 50, 35), 'numero_operacion': '5695993249455', 'msisdn': '51949489191', 'nro_suministro': '506786', 'registro': 'PEA716C23501482025-06-1018.50.37948610460000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('30.00')}, {'financial_id': Decimal('5902506101855283482738'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 55, 28), 'numero_operacion': '5695993249530', 'msisdn': '51981050344', 'nro_suministro': '052671914', 'registro': 'PEA716C23550632025-06-1018.55.29948710470000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('385.41')}, {'financial_id': Decimal('8502506101858188902424'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 58, 18), 'numero_operacion': '5695993249597', 'msisdn': '51921151811', 'nro_suministro': '15704926', 'registro': 'PEA716C23574422025-06-1018.58.19948810480000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('43.70')}, {'financial_id': Decimal('1162506101859293012953'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 59, 29), 'numero_operacion': '5695993249636', 'msisdn': '51968785328', 'nro_suministro': '15514897', 'registro': 'PEA716C23590472025-06-1018.59.29948910490000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('111.90')}, {'financial_id': Decimal('5902506101859458013130'), 'payment_time': datetime.datetime(2025, 6, 10, 18, 59, 45), 'numero_operacion': '5695993249645', 'msisdn': '51906759209', 'nro_suministro': '920419418', 'registro': 'PEA716C23592402025-06-1018.59.46949010500000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('8502506101900272102625'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 0, 26), 'numero_operacion': '5695993249652', 'msisdn': '51940800114', 'nro_suministro': '70187077', 'registro': 'PEA716C00000002025-06-1019.00.28949110520000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('99.00')}, {'financial_id': Decimal('1162506101902030313177'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 2, 2), 'numero_operacion': '5695993249670', 'msisdn': '51968785328', 'nro_suministro': '10727140', 'registro': 'PEA716C00015022025-06-1019.02.04949210530000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('13.20')}, {'financial_id': Decimal('1162506101902259943214'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 2, 25), 'numero_operacion': '5695993249700', 'msisdn': '51916995908', 'nro_suministro': '970476309', 'registro': 'PEA716C00021252025-06-1019.02.26949310540000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 2, 44), 'numero_operacion': '0', 'msisdn': '51990349434', 'nro_suministro': '990349434 ', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('80.20')}, {'financial_id': Decimal('1162506101902488633248'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 2, 48), 'numero_operacion': '5695993249709', 'msisdn': '51968785328', 'nro_suministro': '10727140', 'registro': 'PEA716C00023692025-06-1019.02.49949410560000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('13.60')}, {'financial_id': Decimal('1162506101903145523284'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 3, 14), 'numero_operacion': '5695993249693', 'msisdn': '51916118599', 'nro_suministro': '917059199', 'registro': 'PEA716C00024992025-06-1019.03.15949510570000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('50.12')}, {'financial_id': Decimal('1162506101903521773342'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 3, 51), 'numero_operacion': '5695993249726', 'msisdn': '51968785328', 'nro_suministro': '10727140', 'registro': 'PEA716C00033852025-06-1019.03.52949610590000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('15.30')}, {'financial_id': Decimal('1162506101903597583362'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 3, 59), 'numero_operacion': '5695993249729', 'msisdn': '51916995908', 'nro_suministro': '916995908', 'registro': 'PEA716C00034582025-06-1019.04.00949710600000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('5902506101904365363582'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 4, 36), 'numero_operacion': '5695993249763', 'msisdn': '51968785328', 'nro_suministro': '10727140', 'registro': 'PEA716C00042142025-06-1019.04.37949810620000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('15.00')}, {'financial_id': Decimal('5902506101907376563886'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 7, 37), 'numero_operacion': '5695993249754', 'msisdn': '51968785328', 'nro_suministro': '10867938', 'registro': 'PEA716C00072252025-06-1019.07.38949910630000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('19.80')}, {'financial_id': Decimal('1162506101908261263766'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 8, 25), 'numero_operacion': '5695993249802', 'msisdn': '51994143491', 'nro_suministro': '10606797', 'registro': 'PEA716C00080842025-06-1019.08.26950010640000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('1162506101908337013779'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 8, 33), 'numero_operacion': '5695993249800', 'msisdn': '51968785328', 'nro_suministro': '10867938', 'registro': 'PEA716C00080332025-06-1019.08.34950110660000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Enosa@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('19.50')}, {'financial_id': Decimal('8502506101912599793774'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 12, 59), 'numero_operacion': '5695993249891', 'msisdn': '51972690689', 'nro_suministro': '969058604', 'registro': 'PEA716C00123992025-06-1019.13.00950210670000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('33.10')}, {'financial_id': Decimal('1162506101914422114297'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 14, 41), 'numero_operacion': '5695993249925', 'msisdn': '51931136667', 'nro_suministro': '61338682', 'registro': 'PEA716C00142312025-06-1019.14.42950310690000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('12.90')}, {'financial_id': Decimal('0'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 15, 18), 'numero_operacion': '0', 'msisdn': '51941608018', 'nro_suministro': '985295818', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('5902506101915213074543'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 15, 20), 'numero_operacion': '5695993249929', 'msisdn': '51931136667', 'nro_suministro': '61338682', 'registro': 'PEA716C00145712025-06-1019.15.21950410700000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('12.10')}, {'financial_id': Decimal('5902506101915534754578'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 15, 53), 'numero_operacion': '5695993249959', 'msisdn': '51931136667', 'nro_suministro': '47842341', 'registro': 'PEA716C00154012025-06-1019.15.54950510710000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('77.80')}, {'financial_id': Decimal('5902506101916069064598'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 16, 6), 'numero_operacion': '5695993249961', 'msisdn': '51941608018', 'nro_suministro': '985295818', 'registro': 'PEA716C00154332025-06-1019.16.07950610720000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.10')}, {'financial_id': Decimal('1162506101916282934463'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 16, 27), 'numero_operacion': '5695993249968', 'msisdn': '51900175438', 'nro_suministro': '051655885', 'registro': 'PEA716C00161482025-06-1019.16.28950710740000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('461.01')}, {'financial_id': Decimal('1162506101916310814468'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 16, 30), 'numero_operacion': '5695993249966', 'msisdn': '51931136667', 'nro_suministro': '47842341', 'registro': 'PEA716C00161302025-06-1019.16.31950810750000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('79.40')}, {'financial_id': Decimal('1162506101925209005227'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 25, 20), 'numero_operacion': '5695993250122', 'msisdn': '51955026139', 'nro_suministro': '2600342', 'registro': 'PEA716C00245372025-06-1019.25.21950910760000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('53.00')}, {'financial_id': Decimal('1162506101927269645410'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 27, 26), 'numero_operacion': '5695993250152', 'msisdn': '51906737809', 'nro_suministro': '906737809', 'registro': 'PEA716C00270922025-06-1019.27.27951010770000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('33.10')}, {'financial_id': Decimal('1162506101932001985809'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 31, 59), 'numero_operacion': '5695993250239', 'msisdn': '51966581084', 'nro_suministro': '50292988', 'registro': 'PEA716C00314082025-06-1019.32.00951110790000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Hidrandina@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('30.40')}, {'financial_id': Decimal('1162506101936285536178'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 36, 28), 'numero_operacion': '5695993250324', 'msisdn': '51922992591', 'nro_suministro': '76218871', 'registro': 'PEA716C00354352025-06-1019.36.29951210800000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.11')}, {'financial_id': Decimal('5902506101936306996302'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 36, 30), 'numero_operacion': '5695993250326', 'msisdn': '51939480677', 'nro_suministro': '76218871', 'registro': 'PEA716C00354612025-06-1019.36.31951310810000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('29.17')}, {'financial_id': Decimal('5902506101936310796303'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 36, 30), 'numero_operacion': '0', 'msisdn': '51980808202', 'nro_suministro': '76218871', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('28.65')}, {'financial_id': Decimal('5902506101937448656398'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 37, 44), 'numero_operacion': '5695993250362', 'msisdn': '51939480677', 'nro_suministro': '70271835', 'registro': 'PEA716C00372052025-06-1019.37.45951510830000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('29.17')}, {'financial_id': Decimal('1162506101937454896288'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 37, 45), 'numero_operacion': '5695993250361', 'msisdn': '51922992591', 'nro_suministro': '70271835', 'registro': 'PEA716C00371912025-06-1019.37.46951610840000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.11')}, {'financial_id': Decimal('1162506101939270886421'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 39, 26), 'numero_operacion': '5695993250383', 'msisdn': '51939480677', 'nro_suministro': '22078248', 'registro': 'PEA716C00383922025-06-1019.39.27951710850000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('29.17')}, {'financial_id': Decimal('1162506101939272736422'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 39, 26), 'numero_operacion': '5695993250386', 'msisdn': '51922992591', 'nro_suministro': '22078248', 'registro': 'PEA716C00384342025-06-1019.39.27951810860000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.11')}, {'financial_id': Decimal('5902506101940276936628'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 40, 27), 'numero_operacion': '5695993250424', 'msisdn': '51922992591', 'nro_suministro': '70560749', 'registro': 'PEA716C00400162025-06-1019.40.28951910870000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.11')}, {'financial_id': Decimal('5902506101940284936630'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 40, 28), 'numero_operacion': '5695993250425', 'msisdn': '51939480677', 'nro_suministro': '70560749', 'registro': 'PEA716C00400532025-06-1019.40.29952010880000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('29.17')}, {'financial_id': Decimal('8502506101943149106299'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 43, 14), 'numero_operacion': '5695993250459', 'msisdn': '51939480677', 'nro_suministro': '22067565', 'registro': 'PEA716C00424772025-06-1019.43.15952110890000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('29.17')}, {'financial_id': Decimal('5902506101943155366860'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 43, 15), 'numero_operacion': '5695993250460', 'msisdn': '51922992591', 'nro_suministro': '22067565', 'registro': 'PEA716C00424822025-06-1019.43.16952210900000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('20.11')}, {'financial_id': Decimal('5902506101944067716927'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 44, 6), 'numero_operacion': '5695993250412', 'msisdn': '51924837560', 'nro_suministro': '43121746', 'registro': 'PEA716C00430612025-06-1019.44.07952310910000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('5902506101945121027011'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 45, 11), 'numero_operacion': '5695993250495', 'msisdn': '51990349434', 'nro_suministro': '990349434 ', 'registro': 'PEA716C00444912025-06-1019.45.12952410930000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('80.20')}, {'financial_id': Decimal('1162506101947242696983'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 47, 23), 'numero_operacion': '5695993250530', 'msisdn': '51922992591', 'nro_suministro': '22062889', 'registro': 'PEA716C00470092025-06-1019.47.24952510950000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('40.25')}, {'financial_id': Decimal('8502506101947247916592'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 47, 24), 'numero_operacion': '5695993250528', 'msisdn': '51939480677', 'nro_suministro': '22062889', 'registro': 'PEA716C00470012025-06-1019.47.25952610960000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('58.34')}, {'financial_id': Decimal('8502506101951198036904'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 51, 19), 'numero_operacion': '5695993250594', 'msisdn': '51928350882', 'nro_suministro': '928350882', 'registro': 'PEA716C00505702025-06-1019.51.20952710970000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506101958048417826'), 'payment_time': datetime.datetime(2025, 6, 10, 19, 58, 4), 'numero_operacion': '5695993250703', 'msisdn': '51917426995', 'nro_suministro': '77479462', 'registro': 'PEA716C00574112025-06-1019.58.05952810990000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('22.00')}, {'financial_id': Decimal('1162506102000454648029'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 0, 45), 'numero_operacion': '0', 'msisdn': '51980808202', 'nro_suministro': '76218871', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('28.65')}, {'financial_id': Decimal('5902506102000507628266'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 0, 50), 'numero_operacion': '5695993250719', 'msisdn': '51902207648', 'nro_suministro': '3159859', 'registro': 'PEA716C00594782025-06-1020.00.51953011020000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('102.50')}, {'financial_id': Decimal('5902506102001189378298'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 1, 18), 'numero_operacion': '0', 'msisdn': '51980808202', 'nro_suministro': '70271835', 'registro': 'Sin registro', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('28.65')}, {'financial_id': Decimal('1162506102001233128082'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 1, 22), 'numero_operacion': '5695993250731', 'msisdn': '51984023218', 'nro_suministro': '956886402', 'registro': 'PEA716C01005612025-06-1020.01.24953211040000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('35.10')}, {'financial_id': Decimal('1162506102001401318105'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 1, 39), 'numero_operacion': '5695993250732', 'msisdn': '51955267975', 'nro_suministro': '955267975', 'registro': 'PEA716C01010232025-06-1020.01.40953311060000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('28.65')}, {'financial_id': Decimal('5902506102002280068383'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 2, 27), 'numero_operacion': '5695993250766', 'msisdn': '51980808202', 'nro_suministro': '70271834', 'registro': 'PEA716C01014882025-06-1020.02.28953411080000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Banco_Pichincha@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('200.59')}, {'financial_id': Decimal('5902506102006386568702'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 6, 38), 'numero_operacion': '5695993250813', 'msisdn': '51987985969', 'nro_suministro': '957938725', 'registro': 'PEA716C01053722025-06-1020.06.39953511090000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('29.10')}, {'financial_id': Decimal('1162506102007227898530'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 7, 22), 'numero_operacion': '5695993250826', 'msisdn': '51919054846', 'nro_suministro': '71245814', 'registro': 'PEA716C01065822025-06-1020.07.23953611110000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Directv@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('16.00')}, {'financial_id': Decimal('1162506102016160159170'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 16, 15), 'numero_operacion': '5695993250949', 'msisdn': '51981405433', 'nro_suministro': '981405433', 'registro': 'PEA716C01154252025-06-1020.16.16953711130000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('128.99')}, {'financial_id': Decimal('1162506102020114399417'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 20, 10), 'numero_operacion': '5695993251004', 'msisdn': '51962096074', 'nro_suministro': '978119864', 'registro': 'PEA716C01183022025-06-1020.20.12953811150000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('26.10')}, {'financial_id': Decimal('5902506102020131029686'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 20, 12), 'numero_operacion': '5695993251019', 'msisdn': '51949841451', 'nro_suministro': '421673', 'registro': 'PEA716C01194682025-06-1020.20.13953911170000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('194.10')}, {'financial_id': Decimal('8502506102024299629302'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 24, 29), 'numero_operacion': '5695993251068', 'msisdn': '51913215591', 'nro_suministro': '913215591', 'registro': 'PEA716C01240602025-06-1020.24.30954011180000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('8502506102026416139470'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 26, 41), 'numero_operacion': '5695993251108', 'msisdn': '51924176331', 'nro_suministro': '907990508', 'registro': 'PEA716C01260782025-06-1020.26.42954111200000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('41.10')}, {'financial_id': Decimal('1162506102030342570100'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 30, 33), 'numero_operacion': '5695993251144', 'msisdn': '51923927192', 'nro_suministro': '923927192', 'registro': 'PEA716C01301502025-06-1020.30.34954211220000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('73.80')}, {'financial_id': Decimal('8502506102042413390532'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 42, 40), 'numero_operacion': '5695993251267', 'msisdn': '51922737661', 'nro_suministro': '922737661', 'registro': 'PEA716C01404952025-06-1020.42.42954311240000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('35.10')}, {'financial_id': Decimal('1162506102045000331032'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 44, 59), 'numero_operacion': '5695993251303', 'msisdn': '51901201498', 'nro_suministro': '5130021', 'registro': 'PEA716C01443072025-06-1020.45.00954411260000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('26.50')}, {'financial_id': Decimal('1162506102058557551823'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 58, 55), 'numero_operacion': '5695993251399', 'msisdn': '51951644912', 'nro_suministro': '904929113', 'registro': 'PEA716C01581012025-06-1020.58.56954511280000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('18.30')}, {'financial_id': Decimal('1162506102059271681865'), 'payment_time': datetime.datetime(2025, 6, 10, 20, 59, 26), 'numero_operacion': '5695993251402', 'msisdn': '51969440011', 'nro_suministro': '4215923', 'registro': 'PEA716C01585352025-06-1020.59.27954611300000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Sedapal@WU', 'service_charge': Decimal('1.00'), 'total': Decimal('189.80')}, {'financial_id': Decimal('8502506102107073441892'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 7, 6), 'numero_operacion': '5695993251488', 'msisdn': '51994516268', 'nro_suministro': '053260896', 'registro': 'PEA716C02062972025-06-1021.07.08954711320000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('100.00')}, {'financial_id': Decimal('1162506102110429482470'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 10, 42), 'numero_operacion': '5695993251516', 'msisdn': '51930855700', 'nro_suministro': '907148272', 'registro': 'PEA716C02095512025-06-1021.10.43954811330000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('31.15')}, {'financial_id': Decimal('1162506102120573373045'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 20, 56), 'numero_operacion': '5695993251575', 'msisdn': '51930661878', 'nro_suministro': '1955018', 'registro': 'PEA716C02193042025-06-1021.20.57954911350000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Pluz@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('150.50')}, {'financial_id': Decimal('1162506102123553673237'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 23, 54), 'numero_operacion': '5695993251471', 'msisdn': '51923946660', 'nro_suministro': '923946660', 'registro': 'PEA716C02230492025-06-1021.23.56955011360000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Entel@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('103.08')}, {'financial_id': Decimal('8502506102126023952921'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 26, 1), 'numero_operacion': '5695993251598', 'msisdn': '51990961281', 'nro_suministro': '1052311', 'registro': 'PEA716C02245092025-06-1021.26.03955111380000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('44.20')}, {'financial_id': Decimal('5902506102126459943510'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 26, 45), 'numero_operacion': '5695993251602', 'msisdn': '51959605017', 'nro_suministro': '959605017', 'registro': 'PEA716C02261902025-06-1021.26.46955211390000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('72.80')}, {'financial_id': Decimal('5902506102127305413551'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 27, 30), 'numero_operacion': '5695993251608', 'msisdn': '51994504092', 'nro_suministro': '994504092', 'registro': 'PEA716C02271362025-06-1021.27.31955311410000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('32.30')}, {'financial_id': Decimal('1162506102135491523876'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 35, 48), 'numero_operacion': '5695993251647', 'msisdn': '51910988404', 'nro_suministro': '910988404', 'registro': 'PEA716C02350462025-06-1021.35.49955411430000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Movistar@WU', 'service_charge': Decimal('1.20'), 'total': Decimal('84.10')}, {'financial_id': Decimal('1162506102137303903958'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 37, 29), 'numero_operacion': '5695993251655', 'msisdn': '51913441336', 'nro_suministro': '314742', 'registro': 'PEA716C02371272025-06-1021.37.31955511450000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Calidda@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('44.10')}, {'financial_id': Decimal('5902506102146326404410'), 'payment_time': datetime.datetime(2025, 6, 10, 21, 46, 32), 'numero_operacion': '5695993251756', 'msisdn': '51956689340', 'nro_suministro': '034508852', 'registro': 'PEA716C02455542025-06-1021.46.33955611460000000000', "CONCAT(REPLACE(empresa, ' ', '_'),'@WU')": 'Esika@WU', 'service_charge': Decimal('0.00'), 'total': Decimal('319.78')}]
pie [{'TOTAL WU': 'TOTAL WU', 'SUM(total)': Decimal('29432.79')}]
Creando archivo.
Escribiendo archivo...
Datos del archivo /home/<USER>/output/mysql/PDP-REPORTE-WU_HUB_20250611.csv
Archivo escrito. /home/<USER>/output/mysql/PDP-REPORTE-WU_HUB_20250611.csv
Archivo cerrado. /home/<USER>/output/mysql/PDP-REPORTE-WU_HUB_20250611.csv
Se creo archivo.
PDP-REPORTE-WU_HUB_20250611.csv
