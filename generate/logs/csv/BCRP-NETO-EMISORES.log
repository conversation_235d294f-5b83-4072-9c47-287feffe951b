2025-06-11 06:41:35,311 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-11 06:41:35,312 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/BCRP-NETO_PAGAR_ENTRE_EMISORES-**************.csv
2025-06-11 06:41:35,324 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-11 06:41:35,601 - root - INFO - Archivo: BCRP-NETO_PAGAR_ENTRE_EMISORES-**************.csv subido a: s3://prd-datalake-reports-************/BCRP/2025-06-11/BCRP-NETO-EMISORES/BCRP-NETO_PAGAR_ENTRE_EMISORES-**************.csv
WITH
DATA_TRX AS (
	SELECT
	"TransactionID" as TRX_<PERSON>,
	"Amount" as <PERSON><PERSON><PERSON>,
	"From_BankDomain" as <PERSON><PERSON><PERSON>,
	"To_BankDomain" as <PERSON><PERSON><PERSON>,
	"<PERSON><PERSON>rency" as <PERSON><PERSON><PERSON><PERSON>,
	"DateTime" AS FECHA
	FROM USR_DATALAKE.LOG_TRX_FINAL
	WHERE "From_BankDomain" <> "To_BankDomain"
	AND TRUNC(TO_DATE("DateTime",'YYYY/MM/DD HH24:MI:SS')) >= TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
),
PRE_ENVIADO AS (
	SELECT 
	PAYER.PAYER,
	PAYER.PAYEE,
	PAYER.MONEDA,
	SUM(PAYER.MONTO) AS MONTO_ENVIADO
	FROM DATA_TRX PAYER
	GROUP BY 
	PAYER.PAYER,
	PAYER.PAYEE,
	PAYER.MONEDA
),
PRE_FINAL AS (
	SELECT 
	P1.PAYER,
	P1.PAYEE,
	P1.MONEDA,
	COALESCE(SUM(P1.MONTO_ENVIADO),0) AS MONTO_ENVIADO,
	COALESCE(SUM(P2.MONTO_ENVIADO),0) AS MONTO_RECIBIDO
	FROM PRE_ENVIADO P1
	LEFT JOIN PRE_ENVIADO P2
	ON P1.PAYEE = P2.PAYER AND P1.PAYER = P2.PAYEE
	GROUP BY 
	P1.PAYER,
	P1.PAYEE,
	P1.MONEDA
)
SELECT 
REPLACE(REPLACE(ID1.ISSUER_CODE,'0144',''),'0231','') ORIGEN,
REPLACE(REPLACE(ID2.ISSUER_CODE,'0144',''),'0231','') DESTINO,
COALESCE(PF.MONTO_ENVIADO,0) AS MONTO_ENVIADO,
COALESCE(PF.MONTO_RECIBIDO,0) AS MONTO_RECIBIDO,
COALESCE((PF.MONTO_ENVIADO-PF.MONTO_RECIBIDO),0) AS MONTO_NETO,
COALESCE(PF.MONEDA,'PEN') AS MONEDA
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID1
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID2 ON ID1.ISSUER_CODE <> ID2.ISSUER_CODE
LEFT JOIN PRE_FINAL PF ON REPLACE(REPLACE(ID1.ISSUER_CODE,'0144',''),'','') = PF.PAYER AND REPLACE(REPLACE(ID2.ISSUER_CODE,'0144',''),'','') = PF.PAYEE

Ejecutando query: WITH
DATA_TRX AS (
	SELECT
	"TransactionID" as TRX_ID,
	"Amount" as MONTO,
	"From_BankDomain" as PAYER,
	"To_BankDomain" as PAYEE,
	"Currency" as MONEDA,
	"DateTime" AS FECHA
	FROM USR_DATALAKE.LOG_TRX_FINAL
	WHERE "From_BankDomain" <> "To_BankDomain"
	AND TRUNC(TO_DATE("DateTime",'YYYY/MM/DD HH24:MI:SS')) >= TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
),
PRE_ENVIADO AS (
	SELECT 
	PAYER.PAYER,
	PAYER.PAYEE,
	PAYER.MONEDA,
	SUM(PAYER.MONTO) AS MONTO_ENVIADO
	FROM DATA_TRX PAYER
	GROUP BY 
	PAYER.PAYER,
	PAYER.PAYEE,
	PAYER.MONEDA
),
PRE_FINAL AS (
	SELECT 
	P1.PAYER,
	P1.PAYEE,
	P1.MONEDA,
	COALESCE(SUM(P1.MONTO_ENVIADO),0) AS MONTO_ENVIADO,
	COALESCE(SUM(P2.MONTO_ENVIADO),0) AS MONTO_RECIBIDO
	FROM PRE_ENVIADO P1
	LEFT JOIN PRE_ENVIADO P2
	ON P1.PAYEE = P2.PAYER AND P1.PAYER = P2.PAYEE
	GROUP BY 
	P1.PAYER,
	P1.PAYEE,
	P1.MONEDA
)
SELECT 
REPLACE(REPLACE(ID1.ISSUER_CODE,'0144',''),'0231','') ORIGEN,
REPLACE(REPLACE(ID2.ISSUER_CODE,'0144',''),'0231','') DESTINO,
COALESCE(PF.MONTO_ENVIADO,0) AS MONTO_ENVIADO,
COALESCE(PF.MONTO_RECIBIDO,0) AS MONTO_RECIBIDO,
COALESCE((PF.MONTO_ENVIADO-PF.MONTO_RECIBIDO),0) AS MONTO_NETO,
COALESCE(PF.MONEDA,'PEN') AS MONEDA
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID1
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID2 ON ID1.ISSUER_CODE <> ID2.ISSUER_CODE
LEFT JOIN PRE_FINAL PF ON REPLACE(REPLACE(ID1.ISSUER_CODE,'0144',''),'','') = PF.PAYER AND REPLACE(REPLACE(ID2.ISSUER_CODE,'0144',''),'','') = PF.PAYEE

[<src.core.models.report_row.ReportRow object at 0x7f6f2539eb20>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ea00>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ebb0>, <src.core.models.report_row.ReportRow object at 0x7f6f2539e970>, <src.core.models.report_row.ReportRow object at 0x7f6f2539e910>, <src.core.models.report_row.ReportRow object at 0x7f6f2539eb80>, <src.core.models.report_row.ReportRow object at 0x7f6f2539e9d0>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ea60>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ea90>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ec10>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ec70>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ecd0>, <src.core.models.report_row.ReportRow object at 0x7f6f2539eb50>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ef70>, <src.core.models.report_row.ReportRow object at 0x7f6f2539eeb0>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ebe0>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ef10>, <src.core.models.report_row.ReportRow object at 0x7f6f2539ee50>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c280>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c2e0>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c340>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c3a0>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c400>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c460>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c4c0>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c520>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c580>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c5e0>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c640>, <src.core.models.report_row.ReportRow object at 0x7f6f2534c6a0>]
