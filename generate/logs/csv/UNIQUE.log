WITH 
DATA_UNIQUE AS (
	SELECT
	"TransferID" AS trx_id,
	"TransferID" AS financial_trx_id,
	"ExternalTransactionID" AS external_trx_id,
	TO_CHAR("TransferDate", 'YYYY-MM-DD HH24:MI:SS') as datetime,
	"From_Msisdn" AS msisdn,
	"Remarks" ||'@'||REPLACE("To_Identifier",'1','') AS complete_username,
	LPAD(SUBSTR(TO_CHAR("Amount"), 1, LENGTH(TO_CHAR("Amount"))-2), LENGTH(TO_CHAR("Amount"))-2, '0') || '.' || SUBSTR(TO_CHAR("Amount"), LENGTH(TO_CHAR("Amount"))-1, 2) AS monto,
	"Amount" AS monto_real
	FROM USR_DATALAKE.PRE_LOG_TRX
	WHERE "TransactionType" = 'EXTERNAL_PAYMENT'
	AND "To_Identifier" LIKE '%unique%'
	AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
)
SELECT 
TRX_ID || ',' ||
FINANCIAL_TRX_ID || ',' ||
EXTERNAL_TRX_ID  || ',' ||
DATETIME  || ',' ||
MSISDN  || ',' ||
COMPLETE_USERNAME  || ',' ||
MONTO AS SALIDA
FROM DATA_UNIQUE
UNION ALL
SELECT 
'TOTAL UNIQUE, ' || 
LPAD(SUBSTR(TO_CHAR(SUM(MONTO_REAL)), 1, LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2, '0') || '.' || SUBSTR(TO_CHAR(SUM(MONTO_REAL)), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-1, 2)
FROM DATA_UNIQUE

Ejecutando query: WITH 
DATA_UNIQUE AS (
	SELECT
	"TransferID" AS trx_id,
	"TransferID" AS financial_trx_id,
	"ExternalTransactionID" AS external_trx_id,
	TO_CHAR("TransferDate", 'YYYY-MM-DD HH24:MI:SS') as datetime,
	"From_Msisdn" AS msisdn,
	"Remarks" ||'@'||REPLACE("To_Identifier",'1','') AS complete_username,
	LPAD(SUBSTR(TO_CHAR("Amount"), 1, LENGTH(TO_CHAR("Amount"))-2), LENGTH(TO_CHAR("Amount"))-2, '0') || '.' || SUBSTR(TO_CHAR("Amount"), LENGTH(TO_CHAR("Amount"))-1, 2) AS monto,
	"Amount" AS monto_real
	FROM USR_DATALAKE.PRE_LOG_TRX
	WHERE "TransactionType" = 'EXTERNAL_PAYMENT'
	AND "To_Identifier" LIKE '%unique%'
	AND TRUNC("TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
)
SELECT 
TRX_ID || ',' ||
FINANCIAL_TRX_ID || ',' ||
EXTERNAL_TRX_ID  || ',' ||
DATETIME  || ',' ||
MSISDN  || ',' ||
COMPLETE_USERNAME  || ',' ||
MONTO AS SALIDA
FROM DATA_UNIQUE
UNION ALL
SELECT 
'TOTAL UNIQUE, ' || 
LPAD(SUBSTR(TO_CHAR(SUM(MONTO_REAL)), 1, LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-2, '0') || '.' || SUBSTR(TO_CHAR(SUM(MONTO_REAL)), LENGTH(TO_CHAR(SUM(MONTO_REAL)))-1, 2)
FROM DATA_UNIQUE

[<src.core.models.report_row.ReportRow object at 0x7ff8d3a49520>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49400>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a495b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a493a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49580>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a493d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49460>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a496a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49700>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49760>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a497c0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49820>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49880>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a498e0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49940>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a499a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49a00>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49a60>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49ac0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49b20>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49b80>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49be0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49c40>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49ca0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49d00>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49f40>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49fa0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49e80>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49e20>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59250>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a592b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59310>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a593d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59430>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a594f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a595b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59670>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a596d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59730>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59790>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a597f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59850>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a598b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59910>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59970>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a599d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59a30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59a90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59af0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59b50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59bb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59c10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59c70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59cd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59d30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59d90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59df0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59e50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59eb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59f10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59f70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59fd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a590a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59070>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59160>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a49d60>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a59040>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d250>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d2b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d310>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d3d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d430>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d4f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d5b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d670>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d6d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d730>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d790>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d7f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d850>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d8b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d910>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d970>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d9d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5da30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5da90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5daf0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5db50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5dbb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5dc10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5dc70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5dcd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5dd30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5dd90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5ddf0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5de50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5deb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5df10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5df70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5dfd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d0a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d070>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d160>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a590d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d040>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62250>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a622b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62310>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a623d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62430>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a624f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a625b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62670>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a626d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62730>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62790>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a627f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62850>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a628b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62910>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62970>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a629d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62a30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62a90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62af0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62b50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62bb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62c10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62c70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62cd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62d30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62d90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62df0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62e50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62eb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62f10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62f70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62fd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a620a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62070>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62160>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a5d0d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a62040>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66250>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a662b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66310>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a663d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66430>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a664f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a665b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66670>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a666d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66730>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66790>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a667f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66850>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a668b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66910>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66970>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a669d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66a30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66a90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66af0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66b50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66bb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66c10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66c70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66cd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66d30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66d90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66df0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66e50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66eb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66f10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66f70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66fd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a660a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66070>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66160>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a620d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a66040>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a250>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a2b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a310>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a3d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a430>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a4f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a5b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a670>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a6d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a730>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a790>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a7f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a850>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a8b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a910>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a970>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a9d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6aa30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6aa90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6aaf0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ab50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6abb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ac10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ac70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6acd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ad30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ad90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6adf0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ae50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6aeb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6af10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6af70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6afd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a0a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a070>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a160>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a660d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a040>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e250>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e2b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e310>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e3d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e430>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e4f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e5b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e670>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e6d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e730>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e790>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e7f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e850>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e8b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e910>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e970>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e9d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ea30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ea90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6eaf0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6eb50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ebb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ec10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ec70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ecd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ed30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ed90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6edf0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ee50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6eeb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ef10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6ef70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6efd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e0a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e070>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e160>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6a0d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e040>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72250>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a722b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72310>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a723d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72430>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a724f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a725b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72670>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a726d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72730>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72790>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a727f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72850>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a728b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72910>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72970>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a729d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72a30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72a90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72af0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72b50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72bb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72c10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72c70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72cd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72d30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72d90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72df0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72e50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72eb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72f10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72f70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72fd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a720a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72070>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72160>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a6e0d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a72040>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76250>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a762b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76310>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a763d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76430>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a764f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a765b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76670>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a766d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76730>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76790>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a767f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76850>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a768b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76910>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76970>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a769d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76a30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76a90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76af0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76b50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76bb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76c10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76c70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76cd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76d30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76d90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76df0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76e50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76eb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76f10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76f70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76fd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a760a0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76070>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76160>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a720d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a76040>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b250>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b2b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b310>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b370>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b3d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b430>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b490>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b4f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b550>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b5b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b610>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b670>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b6d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b730>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b790>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b7f0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b850>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b8b0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b910>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b970>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b9d0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7ba30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7ba90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7baf0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bb50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bbb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bc10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bc70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bcd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bd30>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bd90>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bdf0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7be50>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7beb0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bf10>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bf70>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7bfd0>, <src.core.models.report_row.ReportRow object at 0x7ff8d3a7b0a0>]2025-06-11 06:45:30,873 - root - INFO - Datos encontrados. Procediendo a exportar el archivo CSV.
2025-06-11 06:45:30,877 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/PDP-REPORTE-UNIQUE-20250611000000.csv
2025-06-11 06:45:30,889 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-11 06:45:31,071 - root - INFO - Archivo: PDP-REPORTE-UNIQUE-20250611000000.csv subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-11/UNIQUE/PDP-REPORTE-UNIQUE-20250611000000.csv

Archivo guardado correctamente en /home/<USER>/output/csv/PDP-REPORTE-UNIQUE-20250611000000.csv
