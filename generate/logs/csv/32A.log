2025-06-11 06:40:43,870 - root - INFO - <PERSON><PERSON> encontrados. Procediendo a exportar el archivo CSV.
2025-06-11 06:40:43,871 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/32A-20250611.csv
WITH 
dt1 AS (
SELECT
	id.ISSUER_CODE as emisor,
	BO.MONTO AS balance
FROM USR_DATALAKE.USER_BALANCES BO 
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS id ON BO.ISSUER_ID = ID.ISSUER_ID
WHERE BO.FECHA_ORIGEN = TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')
),
dt2 AS (
select 
replace(emisor,'0144','') as emisor,
sum(balance) as monto
from dt1
group by 
replace(emisor,'0144','')
)
SELECT 
	EMISOR,
	LPAD(100,6,'0')||RPAD(LPAD(MONTO,18,'0'),72,'0') AS MONTO
FROM dt2

Ejecutando query: WITH 
dt1 AS (
SELECT
	id.ISSUER_CODE as emisor,
	BO.MONTO AS balance
FROM USR_DATALAKE.USER_BALANCES BO 
INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS id ON BO.ISSUER_ID = ID.ISSUER_ID
WHERE BO.FECHA_ORIGEN = TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')
),
dt2 AS (
select 
replace(emisor,'0144','') as emisor,
sum(balance) as monto
from dt1
group by 
replace(emisor,'0144','')
)
SELECT 
	EMISOR,
	LPAD(100,6,'0')||RPAD(LPAD(MONTO,18,'0'),72,'0') AS MONTO
FROM dt2

[<src.core.models.report_row.ReportRow object at 0x7fef0825ea30>, <src.core.models.report_row.ReportRow object at 0x7fef0825e910>, <src.core.models.report_row.ReportRow object at 0x7fef0825eb50>, <src.core.models.report_row.ReportRow object at 0x7fef0825ebb0>, <src.core.models.report_row.ReportRow object at 0x7fef0825ec10>]
