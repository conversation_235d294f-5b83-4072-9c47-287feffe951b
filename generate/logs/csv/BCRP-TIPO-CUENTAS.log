2025-06-11 06:45:19,218 - root - INFO - <PERSON><PERSON> encontrados. Procediendo a exportar el archivo CSV.
2025-06-11 06:45:19,220 - root - INFO - Archivo CSV exportado a /home/<USER>/output/csv/BCRP-TIPO_CUENTAS_DINERO_ELECTRONICO-20250611000000.csv
2025-06-11 06:45:19,232 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-11 06:45:19,418 - root - INFO - Archivo: BCRP-TIPO_CUENTAS_DINERO_ELECTRONICO-20250611000000.csv subido a: s3://prd-datalake-reports-637423440311/BCRP/2025-06-11/BCRP-TIPO-CUENTAS/BCRP-TIPO_CUENTAS_DINERO_ELECTRONICO-20250611000000.csv
WITH DATA_USER AS (
	SELECT
	UDT.ISSUER_CODE AS EMISOR,
	UDT.USER_ID AS USER_ID,
	CASE WHEN UDT.ID_TYPE IN ('CE','DNI') THEN 'NATURAL'
	ELSE 'OTROS' END AS T_PERSONA,
	UDT.STATUS,
	UDT.PROFILE AS PROFILE,
	UDT.WALLET_NUMBER,
	UDT.CREATED_ON,
	COALESCE(UB.MONTO,0) AS MONTO
	FROM USER_DATA_TRX udt
	LEFT JOIN USER_BALANCES ub ON UDT.WALLET_NUMBER = UB.WALLET_NUMBER AND UB.FECHA_ORIGEN = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
),
PRE_FINAL AS (
	SELECT 
	EMISOR,
	CASE WHEN CREATED_ON = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')) AND T_PERSONA = 'NATURAL' THEN 1 ELSE 0 END AS C_AFILIADAS,
	CASE WHEN STATUS = 'Y' AND T_PERSONA = 'NATURAL' THEN 1 ELSE 0 END AS C_ACTIVAS,
	CASE WHEN MONTO > 0 AND T_PERSONA = 'NATURAL' THEN 1 ELSE 0 END AS C_SALDO,
	CASE WHEN T_PERSONA = 'NATURAL' THEN MONTO ELSE 0 END AS SALDO,
	CASE WHEN CREATED_ON = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')) AND PROFILE LIKE '%COMERCIO%' THEN 1 ELSE 0 END AS C_COMERCIO,
	CASE WHEN STATUS = 'Y' AND PROFILE LIKE '%COMERCIO%' THEN 1 ELSE 0 END AS A_COMERCIO,
	CASE WHEN MONTO > 0 AND PROFILE LIKE '%COMERCIO%' THEN 1 ELSE 0 END AS S_COMERCIO,
	CASE WHEN PROFILE LIKE '%COMERCIO%' THEN MONTO ELSE 0 END AS SALDO_COMERCIO
	FROM DATA_USER
)
SELECT
	ID.ISSUER_CODE AS Emisor,
	COALESCE(SUM(C_AFILIADAS),0) AS Persona_Natural_Numero_Cuentas_Afiliadas,
	COALESCE(SUM(C_ACTIVAS),0) AS Persona_Natural_Numero_Cuentas_Activas,
	COALESCE(SUM(C_SALDO),0) AS Persona_Natural_Numero_Cuentas_Con_Saldo,
	COALESCE(SUM(SALDO/100),0) AS Persona_Natural_Saldo_Cuentas,
	COALESCE(SUM(C_COMERCIO),0) AS Comercio_Numero_Cuentas_Afiliadas,
	COALESCE(SUM(A_COMERCIO),0) AS Comercio_Numero_Cuentas_Activas,
	COALESCE(SUM(S_COMERCIO),0) AS Comercio_Numero_Cuentas_Con_Saldo,
	COALESCE(SUM(SALDO_COMERCIO/100),0) AS Comercio_Saldo_Cuentas,
	'PEN' AS Moneda
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN PRE_FINAL PF 
ON REPLACE(ID.ISSUER_CODE,'0144','') = PF.EMISOR
GROUP BY ID.ISSUER_CODE

Ejecutando query: WITH DATA_USER AS (
	SELECT
	UDT.ISSUER_CODE AS EMISOR,
	UDT.USER_ID AS USER_ID,
	CASE WHEN UDT.ID_TYPE IN ('CE','DNI') THEN 'NATURAL'
	ELSE 'OTROS' END AS T_PERSONA,
	UDT.STATUS,
	UDT.PROFILE AS PROFILE,
	UDT.WALLET_NUMBER,
	UDT.CREATED_ON,
	COALESCE(UB.MONTO,0) AS MONTO
	FROM USER_DATA_TRX udt
	LEFT JOIN USER_BALANCES ub ON UDT.WALLET_NUMBER = UB.WALLET_NUMBER AND UB.FECHA_ORIGEN = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
),
PRE_FINAL AS (
	SELECT 
	EMISOR,
	CASE WHEN CREATED_ON = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')) AND T_PERSONA = 'NATURAL' THEN 1 ELSE 0 END AS C_AFILIADAS,
	CASE WHEN STATUS = 'Y' AND T_PERSONA = 'NATURAL' THEN 1 ELSE 0 END AS C_ACTIVAS,
	CASE WHEN MONTO > 0 AND T_PERSONA = 'NATURAL' THEN 1 ELSE 0 END AS C_SALDO,
	CASE WHEN T_PERSONA = 'NATURAL' THEN MONTO ELSE 0 END AS SALDO,
	CASE WHEN CREATED_ON = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')) AND PROFILE LIKE '%COMERCIO%' THEN 1 ELSE 0 END AS C_COMERCIO,
	CASE WHEN STATUS = 'Y' AND PROFILE LIKE '%COMERCIO%' THEN 1 ELSE 0 END AS A_COMERCIO,
	CASE WHEN MONTO > 0 AND PROFILE LIKE '%COMERCIO%' THEN 1 ELSE 0 END AS S_COMERCIO,
	CASE WHEN PROFILE LIKE '%COMERCIO%' THEN MONTO ELSE 0 END AS SALDO_COMERCIO
	FROM DATA_USER
)
SELECT
	ID.ISSUER_CODE AS Emisor,
	COALESCE(SUM(C_AFILIADAS),0) AS Persona_Natural_Numero_Cuentas_Afiliadas,
	COALESCE(SUM(C_ACTIVAS),0) AS Persona_Natural_Numero_Cuentas_Activas,
	COALESCE(SUM(C_SALDO),0) AS Persona_Natural_Numero_Cuentas_Con_Saldo,
	COALESCE(SUM(SALDO/100),0) AS Persona_Natural_Saldo_Cuentas,
	COALESCE(SUM(C_COMERCIO),0) AS Comercio_Numero_Cuentas_Afiliadas,
	COALESCE(SUM(A_COMERCIO),0) AS Comercio_Numero_Cuentas_Activas,
	COALESCE(SUM(S_COMERCIO),0) AS Comercio_Numero_Cuentas_Con_Saldo,
	COALESCE(SUM(SALDO_COMERCIO/100),0) AS Comercio_Saldo_Cuentas,
	'PEN' AS Moneda
FROM PDP_PROD10_MAINDB.ISSUER_DETAILS ID LEFT JOIN PRE_FINAL PF 
ON REPLACE(ID.ISSUER_CODE,'0144','') = PF.EMISOR
GROUP BY ID.ISSUER_CODE

[<src.core.models.report_row.ReportRow object at 0x7f945439eaf0>, <src.core.models.report_row.ReportRow object at 0x7f945439e9a0>, <src.core.models.report_row.ReportRow object at 0x7f945439eb80>, <src.core.models.report_row.ReportRow object at 0x7f945439e910>, <src.core.models.report_row.ReportRow object at 0x7f945439e8e0>, <src.core.models.report_row.ReportRow object at 0x7f945439eb50>]
