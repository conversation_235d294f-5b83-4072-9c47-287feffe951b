/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-11 06:39:38,910 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:38,943 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:38,943 - root - INFO - Signing file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FCOMPARTAMOS-********.xlsx
2025-06-11 06:39:38,944 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FCOMPARTAMOS-********.xlsx.signature
2025-06-11 06:39:38,944 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FCOMPARTAMOS-********.xlsx with signature: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FCOMPARTAMOS-********.xlsx.signature
2025-06-11 06:39:38,944 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:38,944 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FCOMPARTAMOS-********.xlsx
2025-06-11 06:39:38,956 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-11 06:39:39,098 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-FCOMPARTAMOS-********.xlsx.signature subido a: s3://prd-datalake-reports-************/FCOMPARTAMOS/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-FCOMPARTAMOS-********.xlsx.signature
2025-06-11 06:39:39,174 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-FCOMPARTAMOS-********.xlsx subido a: s3://prd-datalake-reports-************/FCOMPARTAMOS/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-FCOMPARTAMOS-********.xlsx
2025-06-11 06:39:41,369 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:41,402 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:41,402 - root - INFO - Signing file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-BNACION-********.xlsx
2025-06-11 06:39:41,402 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-BNACION-********.xlsx.signature
2025-06-11 06:39:41,402 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-BNACION-********.xlsx with signature: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-BNACION-********.xlsx.signature
2025-06-11 06:39:41,402 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:41,403 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-BNACION-********.xlsx
2025-06-11 06:39:41,484 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-BNACION-********.xlsx.signature subido a: s3://prd-datalake-reports-************/BNACION/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-BNACION-********.xlsx.signature
2025-06-11 06:39:41,580 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-BNACION-********.xlsx subido a: s3://prd-datalake-reports-************/BNACION/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-BNACION-********.xlsx
2025-06-11 06:39:43,753 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:43,786 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:43,786 - root - INFO - Signing file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CRANDES-********.xlsx
2025-06-11 06:39:43,787 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CRANDES-********.xlsx.signature
2025-06-11 06:39:43,787 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CRANDES-********.xlsx with signature: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CRANDES-********.xlsx.signature
2025-06-11 06:39:43,787 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:43,787 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CRANDES-********.xlsx
2025-06-11 06:39:43,870 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-CRANDES-********.xlsx.signature subido a: s3://prd-datalake-reports-************/CRANDES/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-CRANDES-********.xlsx.signature
2025-06-11 06:39:43,952 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-CRANDES-********.xlsx subido a: s3://prd-datalake-reports-************/CRANDES/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-CRANDES-********.xlsx
2025-06-11 06:39:46,153 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:46,186 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:46,186 - root - INFO - Signing file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CCUSCO-********.xlsx
2025-06-11 06:39:46,186 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CCUSCO-********.xlsx.signature
2025-06-11 06:39:46,187 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CCUSCO-********.xlsx with signature: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CCUSCO-********.xlsx.signature
2025-06-11 06:39:46,187 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:46,187 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-CCUSCO-********.xlsx
2025-06-11 06:39:46,266 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-CCUSCO-********.xlsx.signature subido a: s3://prd-datalake-reports-************/CCUSCO/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-CCUSCO-********.xlsx.signature
2025-06-11 06:39:46,361 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-CCUSCO-********.xlsx subido a: s3://prd-datalake-reports-************/CCUSCO/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-CCUSCO-********.xlsx
2025-06-11 06:39:48,529 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:48,562 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:48,562 - root - INFO - Signing file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-0231FCONFIANZA-********.xlsx
2025-06-11 06:39:48,562 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-0231FCONFIANZA-********.xlsx.signature
2025-06-11 06:39:48,562 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-0231FCONFIANZA-********.xlsx with signature: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-0231FCONFIANZA-********.xlsx.signature
2025-06-11 06:39:48,563 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:48,563 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-0231FCONFIANZA-********.xlsx
2025-06-11 06:39:48,647 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-0231FCONFIANZA-********.xlsx.signature subido a: s3://prd-datalake-reports-************/0231FCONFIANZA/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-0231FCONFIANZA-********.xlsx.signature
2025-06-11 06:39:48,747 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-0231FCONFIANZA-********.xlsx subido a: s3://prd-datalake-reports-************/0231FCONFIANZA/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-0231FCONFIANZA-********.xlsx
/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-11 06:39:50,909 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:50,942 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:50,942 - root - INFO - Signing file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FQAPAQ-********.xlsx
2025-06-11 06:39:50,943 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FQAPAQ-********.xlsx.signature
2025-06-11 06:39:50,943 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FQAPAQ-********.xlsx with signature: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FQAPAQ-********.xlsx.signature
2025-06-11 06:39:50,943 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:50,943 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/REPORTE-TRAZABILIDADFEE-FQAPAQ-********.xlsx
2025-06-11 06:39:51,031 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-FQAPAQ-********.xlsx.signature subido a: s3://prd-datalake-reports-************/FQAPAQ/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-FQAPAQ-********.xlsx.signature
2025-06-11 06:39:51,104 - root - INFO - Archivo: REPORTE-TRAZABILIDADFEE-FQAPAQ-********.xlsx subido a: s3://prd-datalake-reports-************/FQAPAQ/2025-06-11/TRAZA-FEE/REPORTE-TRAZABILIDADFEE-FQAPAQ-********.xlsx
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = '{emisor}'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

Conexión a Oracle establecida.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = 'FCOMPARTAMOS'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = '{emisor}'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

Conexión a Oracle establecida.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = 'BNACION'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = '{emisor}'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

Conexión a Oracle establecida.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = 'CRANDES'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = '{emisor}'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

Conexión a Oracle establecida.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = 'CCUSCO'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = '{emisor}'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

Conexión a Oracle establecida.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = 'FCONFIANZA'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = '{emisor}'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

Conexión a Oracle establecida.
SELECT
"TransactionType" AS "Pago del servicio",
COUNT("TransferID_Mob") AS "Numero de transacciones",
SUM("Amount")/100 AS "Monto cobrado",
COALESCE(SUM("Fee")/100,0) AS "Comision",
"From_BankDomain" AS "Emisor"
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE 1=1
AND MTH."From_BankDomain" = 'FQAPAQ'
AND TRUNC(MTH."TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
GROUP BY "TransactionType", "From_BankDomain"

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
