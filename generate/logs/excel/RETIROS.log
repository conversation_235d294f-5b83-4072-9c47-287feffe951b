/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-11 06:39:53,477 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:53,510 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:53,510 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250611013953.xls
2025-06-11 06:39:53,512 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250611013953.xls.signature
2025-06-11 06:39:53,512 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250611013953.xls with signature: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250611013953.xls.signature
2025-06-11 06:39:53,512 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:53,512 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-FCOMPARTAMOS-20250611013953.xls
2025-06-11 06:39:53,524 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-11 06:39:53,675 - root - INFO - Archivo: RETIROS-FCOMPARTAMOS-20250611013953.xls.signature subido a: s3://prd-datalake-reports-637423440311/FCOMPARTAMOS/2025-06-11/RETIROS/RETIROS-FCOMPARTAMOS-20250611013953.xls.signature
2025-06-11 06:39:53,771 - root - INFO - Archivo: RETIROS-FCOMPARTAMOS-20250611013953.xls subido a: s3://prd-datalake-reports-637423440311/FCOMPARTAMOS/2025-06-11/RETIROS/RETIROS-FCOMPARTAMOS-20250611013953.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='FCOMPARTAMOS'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
2025-06-11 06:39:53,858 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:53,891 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:53,891 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-BNACION-20250611013953.xls
2025-06-11 06:39:53,892 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-BNACION-20250611013953.xls.signature
2025-06-11 06:39:53,892 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-BNACION-20250611013953.xls with signature: /home/<USER>/output/excel/RETIROS-BNACION-20250611013953.xls.signature
2025-06-11 06:39:53,892 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:53,892 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-BNACION-20250611013953.xls
2025-06-11 06:39:53,972 - root - INFO - Archivo: RETIROS-BNACION-20250611013953.xls.signature subido a: s3://prd-datalake-reports-637423440311/BNACION/2025-06-11/RETIROS/RETIROS-BNACION-20250611013953.xls.signature
2025-06-11 06:39:54,050 - root - INFO - Archivo: RETIROS-BNACION-20250611013953.xls subido a: s3://prd-datalake-reports-637423440311/BNACION/2025-06-11/RETIROS/RETIROS-BNACION-20250611013953.xls
2025-06-11 06:39:54,136 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:54,169 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:54,169 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-CRANDES-20250611013953.xls
2025-06-11 06:39:54,170 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-CRANDES-20250611013953.xls.signature
2025-06-11 06:39:54,170 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-CRANDES-20250611013953.xls with signature: /home/<USER>/output/excel/RETIROS-CRANDES-20250611013953.xls.signature
2025-06-11 06:39:54,170 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:54,170 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-CRANDES-20250611013953.xls
2025-06-11 06:39:54,260 - root - INFO - Archivo: RETIROS-CRANDES-20250611013953.xls.signature subido a: s3://prd-datalake-reports-637423440311/CRANDES/2025-06-11/RETIROS/RETIROS-CRANDES-20250611013953.xls.signature
2025-06-11 06:39:54,346 - root - INFO - Archivo: RETIROS-CRANDES-20250611013953.xls subido a: s3://prd-datalake-reports-637423440311/CRANDES/2025-06-11/RETIROS/RETIROS-CRANDES-20250611013953.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='BNACION'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='CRANDES'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
2025-06-11 06:39:54,430 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:54,463 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:54,463 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-CCUSCO-20250611013953.xls
2025-06-11 06:39:54,464 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-CCUSCO-20250611013953.xls.signature
2025-06-11 06:39:54,464 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-CCUSCO-20250611013953.xls with signature: /home/<USER>/output/excel/RETIROS-CCUSCO-20250611013953.xls.signature
2025-06-11 06:39:54,464 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:54,464 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-CCUSCO-20250611013953.xls
2025-06-11 06:39:54,562 - root - INFO - Archivo: RETIROS-CCUSCO-20250611013953.xls.signature subido a: s3://prd-datalake-reports-637423440311/CCUSCO/2025-06-11/RETIROS/RETIROS-CCUSCO-20250611013953.xls.signature
2025-06-11 06:39:54,639 - root - INFO - Archivo: RETIROS-CCUSCO-20250611013953.xls subido a: s3://prd-datalake-reports-637423440311/CCUSCO/2025-06-11/RETIROS/RETIROS-CCUSCO-20250611013953.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='CCUSCO'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
2025-06-11 06:39:54,725 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:54,757 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:54,757 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250611013953.xls
2025-06-11 06:39:54,758 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250611013953.xls.signature
2025-06-11 06:39:54,758 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250611013953.xls with signature: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250611013953.xls.signature
2025-06-11 06:39:54,758 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:54,758 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-0231FCONFIANZA-20250611013953.xls
2025-06-11 06:39:54,833 - root - INFO - Archivo: RETIROS-0231FCONFIANZA-20250611013953.xls.signature subido a: s3://prd-datalake-reports-637423440311/0231FCONFIANZA/2025-06-11/RETIROS/RETIROS-0231FCONFIANZA-20250611013953.xls.signature
2025-06-11 06:39:54,911 - root - INFO - Archivo: RETIROS-0231FCONFIANZA-20250611013953.xls subido a: s3://prd-datalake-reports-637423440311/0231FCONFIANZA/2025-06-11/RETIROS/RETIROS-0231FCONFIANZA-20250611013953.xls
2025-06-11 06:39:54,984 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:55,018 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:55,018 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls
2025-06-11 06:39:55,019 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls.signature
2025-06-11 06:39:55,019 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls with signature: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls.signature
2025-06-11 06:39:55,019 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:55,019 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls
2025-06-11 06:39:55,103 - root - INFO - Archivo: RETIROS-FQAPAQ-20250611013953.xls.signature subido a: s3://prd-datalake-reports-637423440311/FQAPAQ/2025-06-11/RETIROS/RETIROS-FQAPAQ-20250611013953.xls.signature
2025-06-11 06:39:55,179 - root - INFO - Archivo: RETIROS-FQAPAQ-20250611013953.xls subido a: s3://prd-datalake-reports-637423440311/FQAPAQ/2025-06-11/RETIROS/RETIROS-FQAPAQ-20250611013953.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='FCONFIANZA'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

Conexión a Oracle establecida.
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='FQAPAQ'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
Conexión a Oracle establecida.
/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-11 06:39:55,254 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:55,287 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:55,287 - root - INFO - Signing file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls
2025-06-11 06:39:55,289 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls.signature
2025-06-11 06:39:55,289 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls with signature: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls.signature
2025-06-11 06:39:55,289 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:55,289 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/RETIROS-FQAPAQ-20250611013953.xls
2025-06-11 06:39:55,379 - root - INFO - Archivo: RETIROS-FQAPAQ-20250611013953.xls.signature subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-11/RETIROS/RETIROS-FQAPAQ-20250611013953.xls.signature
2025-06-11 06:39:55,501 - root - INFO - Archivo: RETIROS-FQAPAQ-20250611013953.xls subido a: s3://prd-datalake-reports-637423440311/PDP_INTERNO/2025-06-11/RETIROS/RETIROS-FQAPAQ-20250611013953.xls
--RETIRO - MODIFICADO JMARQUINA / 05-03-2025-----
--REQUERIMIENTO PENDIENTE : Nombre de la Hoja sea "Sheet0" ---
----NOMBRE DE ARCHIVO :  RETIROS-BBBBB-YYYYMMDD.xls

SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha", --Se Modificó el campo ID --
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON 
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYER_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC  --Se agregó la siguiente condicional --

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
