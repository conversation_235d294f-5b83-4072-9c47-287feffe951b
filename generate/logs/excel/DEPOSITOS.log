/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-11 06:39:58,447 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:58,480 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:58,480 - root - INFO - Signing file: /home/<USER>/output/excel/DEPOSITOS-FCOMPARTAMOS-20250611013958.xls
2025-06-11 06:39:58,481 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/DEPOSITOS-FCOMPARTAMOS-20250611013958.xls.signature
2025-06-11 06:39:58,481 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/DEPOSITOS-FCOMPARTAMOS-20250611013958.xls with signature: /home/<USER>/output/excel/DEPOSITOS-FCOMPARTAMOS-20250611013958.xls.signature
2025-06-11 06:39:58,481 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:58,481 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/DEPOSITOS-FCOMPARTAMOS-20250611013958.xls
2025-06-11 06:39:58,494 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-11 06:39:58,639 - root - INFO - Archivo: DEPOSITOS-FCOMPARTAMOS-20250611013958.xls.signature subido a: s3://prd-datalake-reports-637423440311/FCOMPARTAMOS/2025-06-11/DEPOSITOS/DEPOSITOS-FCOMPARTAMOS-20250611013958.xls.signature
2025-06-11 06:39:58,725 - root - INFO - Archivo: DEPOSITOS-FCOMPARTAMOS-20250611013958.xls subido a: s3://prd-datalake-reports-637423440311/FCOMPARTAMOS/2025-06-11/DEPOSITOS/DEPOSITOS-FCOMPARTAMOS-20250611013958.xls
2025-06-11 06:39:58,810 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:58,843 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:58,843 - root - INFO - Signing file: /home/<USER>/output/excel/DEPOSITOS-BNACION-20250611013958.xls
2025-06-11 06:39:58,844 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/DEPOSITOS-BNACION-20250611013958.xls.signature
2025-06-11 06:39:58,844 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/DEPOSITOS-BNACION-20250611013958.xls with signature: /home/<USER>/output/excel/DEPOSITOS-BNACION-20250611013958.xls.signature
2025-06-11 06:39:58,844 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:58,844 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/DEPOSITOS-BNACION-20250611013958.xls
2025-06-11 06:39:58,923 - root - INFO - Archivo: DEPOSITOS-BNACION-20250611013958.xls.signature subido a: s3://prd-datalake-reports-637423440311/BNACION/2025-06-11/DEPOSITOS/DEPOSITOS-BNACION-20250611013958.xls.signature
2025-06-11 06:39:58,995 - root - INFO - Archivo: DEPOSITOS-BNACION-20250611013958.xls subido a: s3://prd-datalake-reports-637423440311/BNACION/2025-06-11/DEPOSITOS/DEPOSITOS-BNACION-20250611013958.xls
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC

Conexión a Oracle establecida.
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='FCOMPARTAMOS'
ORDER BY nrd.NETTING_RECORD_ID DESC

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC

Conexión a Oracle establecida.
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='BNACION'
ORDER BY nrd.NETTING_RECORD_ID DESC

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
2025-06-11 06:39:59,077 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:59,110 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:59,110 - root - INFO - Signing file: /home/<USER>/output/excel/DEPOSITOS-CRANDES-20250611013958.xls
2025-06-11 06:39:59,111 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/DEPOSITOS-CRANDES-20250611013958.xls.signature
2025-06-11 06:39:59,111 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/DEPOSITOS-CRANDES-20250611013958.xls with signature: /home/<USER>/output/excel/DEPOSITOS-CRANDES-20250611013958.xls.signature
2025-06-11 06:39:59,111 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:59,111 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/DEPOSITOS-CRANDES-20250611013958.xls
2025-06-11 06:39:59,184 - root - INFO - Archivo: DEPOSITOS-CRANDES-20250611013958.xls.signature subido a: s3://prd-datalake-reports-637423440311/CRANDES/2025-06-11/DEPOSITOS/DEPOSITOS-CRANDES-20250611013958.xls.signature
2025-06-11 06:39:59,260 - root - INFO - Archivo: DEPOSITOS-CRANDES-20250611013958.xls subido a: s3://prd-datalake-reports-637423440311/CRANDES/2025-06-11/DEPOSITOS/DEPOSITOS-CRANDES-20250611013958.xls
2025-06-11 06:39:59,343 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:59,375 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:59,375 - root - INFO - Signing file: /home/<USER>/output/excel/DEPOSITOS-CCUSCO-20250611013958.xls
2025-06-11 06:39:59,376 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/DEPOSITOS-CCUSCO-20250611013958.xls.signature
2025-06-11 06:39:59,376 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/DEPOSITOS-CCUSCO-20250611013958.xls with signature: /home/<USER>/output/excel/DEPOSITOS-CCUSCO-20250611013958.xls.signature
2025-06-11 06:39:59,376 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:59,376 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/DEPOSITOS-CCUSCO-20250611013958.xls
2025-06-11 06:39:59,449 - root - INFO - Archivo: DEPOSITOS-CCUSCO-20250611013958.xls.signature subido a: s3://prd-datalake-reports-637423440311/CCUSCO/2025-06-11/DEPOSITOS/DEPOSITOS-CCUSCO-20250611013958.xls.signature
2025-06-11 06:39:59,526 - root - INFO - Archivo: DEPOSITOS-CCUSCO-20250611013958.xls subido a: s3://prd-datalake-reports-637423440311/CCUSCO/2025-06-11/DEPOSITOS/DEPOSITOS-CCUSCO-20250611013958.xls
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC

Conexión a Oracle establecida.
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='CRANDES'
ORDER BY nrd.NETTING_RECORD_ID DESC

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC

Conexión a Oracle establecida.
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='CCUSCO'
ORDER BY nrd.NETTING_RECORD_ID DESC

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
2025-06-11 06:39:59,610 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:59,642 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:59,642 - root - INFO - Signing file: /home/<USER>/output/excel/DEPOSITOS-0231FCONFIANZA-20250611013958.xls
2025-06-11 06:39:59,643 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/DEPOSITOS-0231FCONFIANZA-20250611013958.xls.signature
2025-06-11 06:39:59,643 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/DEPOSITOS-0231FCONFIANZA-20250611013958.xls with signature: /home/<USER>/output/excel/DEPOSITOS-0231FCONFIANZA-20250611013958.xls.signature
2025-06-11 06:39:59,643 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:59,644 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/DEPOSITOS-0231FCONFIANZA-20250611013958.xls
2025-06-11 06:39:59,710 - root - INFO - Archivo: DEPOSITOS-0231FCONFIANZA-20250611013958.xls.signature subido a: s3://prd-datalake-reports-637423440311/0231FCONFIANZA/2025-06-11/DEPOSITOS/DEPOSITOS-0231FCONFIANZA-20250611013958.xls.signature
2025-06-11 06:39:59,803 - root - INFO - Archivo: DEPOSITOS-0231FCONFIANZA-20250611013958.xls subido a: s3://prd-datalake-reports-637423440311/0231FCONFIANZA/2025-06-11/DEPOSITOS/DEPOSITOS-0231FCONFIANZA-20250611013958.xls
2025-06-11 06:39:59,877 - root - INFO - Loading private key from: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:59,910 - root - INFO - FileSigner initialized with private key: /home/<USER>/generate/FileSigner/pdp_sign.key
2025-06-11 06:39:59,910 - root - INFO - Signing file: /home/<USER>/output/excel/DEPOSITOS-FQAPAQ-20250611013958.xls
2025-06-11 06:39:59,911 - root - INFO - File signed. Signature saved to: /home/<USER>/output/excel/DEPOSITOS-FQAPAQ-20250611013958.xls.signature
2025-06-11 06:39:59,911 - root - INFO - Verifying signature for file: /home/<USER>/output/excel/DEPOSITOS-FQAPAQ-20250611013958.xls with signature: /home/<USER>/output/excel/DEPOSITOS-FQAPAQ-20250611013958.xls.signature
2025-06-11 06:39:59,911 - root - INFO - Loading public key from certificate: /home/<USER>/generate/FileSigner/SignFileNC.crt
2025-06-11 06:39:59,911 - root - INFO - Signature verification succeeded for file: /home/<USER>/output/excel/DEPOSITOS-FQAPAQ-20250611013958.xls
2025-06-11 06:39:59,981 - root - INFO - Archivo: DEPOSITOS-FQAPAQ-20250611013958.xls.signature subido a: s3://prd-datalake-reports-637423440311/FQAPAQ/2025-06-11/DEPOSITOS/DEPOSITOS-FQAPAQ-20250611013958.xls.signature
2025-06-11 06:40:00,060 - root - INFO - Archivo: DEPOSITOS-FQAPAQ-20250611013958.xls subido a: s3://prd-datalake-reports-637423440311/FQAPAQ/2025-06-11/DEPOSITOS/DEPOSITOS-FQAPAQ-20250611013958.xls
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC

Conexión a Oracle establecida.
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='FCONFIANZA'
ORDER BY nrd.NETTING_RECORD_ID DESC

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='{emisor}'
ORDER BY nrd.NETTING_RECORD_ID DESC

Conexión a Oracle establecida.
SELECT nrd.FUND_TRANSFER_ID "ID",to_char(nrd.CREATED_ON,'yyyy-mm-dd hh24:mi') "Fecha",
CASE WHEN mth.TRANSFER_ID IS NULL AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Creado'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='FAILED' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Fallido'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito'--Se Modificó el nombre del estado --
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS='SUCCESS' AND nrd.DEP_PROCESS_STATUS IS NULL THEN 'Retiro realizado esperando respuesta'
WHEN mth.TRANSFER_ID IS NOT null AND nrd.PAR_PROCESS_STATUS IS NULL AND nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Deposito realizado esperando respuesta'
END "Estado",CASE WHEN nrd.PAR_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "PAR-Estado",CASE WHEN nrd.DEP_PROCESS_STATUS='SUCCESS' THEN 'Exito' END "DEP-Estado",
'Netting report del '||to_char(nrd.CREATED_ON,'dd-MM-yyyy') "Descripción",'' "Motivo de error",nrd.PAYER_ISSUER_CODE "De",nrd.PAYEE_ISSUER_CODE "Para",nrd.currency "Moneda",nrd.DIFFERENCE_AMOUNT "Monto"
FROM STOCK_SERVICE_PROD.NETTING_RECORDS_DATA  nrd LEFT JOIN PDP_PROD10_MAINDBBUS.mtx_transaction_header mth ON
(nrd.FUND_TRANSFER_ID=mth.FTXN_ID) INNER JOIN PDP_PROD10_MAINDB.ISSUER_DETAILS ID ON nrd.PAYEE_ISSUER_ID = ID.ISSUER_ID
WHERE NETTING_DATE>=trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'),'YEAR')
AND NETTING_DATE<trunc(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS')+1)
AND REPLACE(REPLACE(ID.ISSUER_CODE,'0144',''),'0231','') ='FQAPAQ'
ORDER BY nrd.NETTING_RECORD_ID DESC

/home/<USER>/generate/FileSigner/pdp_sign.key
Conexión cerrada.
