/home/<USER>/generate/exports_excel/export_to_excel.py:28: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, con=connection)
2025-06-11 06:39:11,410 - botocore.credentials - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-11 06:39:11,710 - root - INFO - Archivo: PDP-REPORTE-SERVICE-PROVIDER-20250611.xlsx subido a: s3://prd-datalake-reports-637423440311/FCOMPARTAMOS/2025-06-11/SERVICE-PROVIDER/PDP-REPORTE-SERVICE-PROVIDER-20250611.xlsx
Conexión a Oracle establecida.
WITH
TRX_MOD AS (
SELECT MTH.*
FROM USR_DATALAKE.PRE_LOG_TRX MTH
WHERE TRUNC("TransferDate") = TRUNC(TO_DATE('2025-06-10 00:00:00','YYYY-MM-DD HH24:MI:SS'))
)
SELECT
CASE 
WHEN MTH."To_LoginID"  LIKE 'lindley%' OR  MTH."From_LoginID" LIKE 'lindley%' THEN 'Recargas Azulito'
WHEN MTH."To_LoginID" LIKE 'airtimebitel%' OR  MTH."From_LoginID" LIKE 'airtimebitel%' THEN 'Recargas Bitel'
WHEN MTH."To_LoginID" LIKE 'airtimeclaro%' OR  MTH."From_LoginID" LIKE 'airtimeclaro%' THEN 'Recargas Claro'
WHEN MTH."To_LoginID" LIKE 'airtimemovistar%' OR  MTH."From_LoginID" LIKE 'airtimemovistar%' THEN 'Recargas Movistar'
WHEN MTH."To_LoginID" LIKE 'airtimeentel%' OR  MTH."From_LoginID" LIKE 'airtimeentel%' THEN 'Recargas Entel'
WHEN MTH."To_LoginID" LIKE 'bitel%' OR  MTH."From_LoginID" LIKE 'bitel%' THEN 'Bitel Postpago'
WHEN MTH."To_LoginID" LIKE 'claro%' OR  MTH."From_LoginID" LIKE 'claro%' THEN 'Claro Postpago'
WHEN MTH."To_LoginID" LIKE 'unique%' OR  MTH."From_LoginID" LIKE 'unique%' THEN 'Unique'
WHEN MTH."To_LoginID" LIKE 'digiflow%' OR  MTH."From_LoginID" LIKE 'digiflow%' THEN 'HUB de Servicios WU'
WHEN MTH."To_LoginID" LIKE 'izipay%' OR  MTH."From_LoginID" LIKE 'izipay%' THEN 'Pagos QR Izipay'
WHEN MTH."To_LoginID" LIKE 'backus%' OR  MTH."From_LoginID" LIKE 'backus%' THEN 'Pagos QR Niubiz'
WHEN MTH."To_LoginID" LIKE 'sentinel%' OR  MTH."From_LoginID" LIKE 'sentinel%' THEN 'Experian'
WHEN MTH."From_Msisdn" = '51989102431' OR  MTH."To_Msisdn" LIKE '51989102431' THEN 'WU Outbound - Envío de Remesas'
WHEN MTH."To_LoginID" LIKE 'WUINBUAT6%' OR  MTH."From_LoginID" LIKE 'WUINBUAT6%' THEN 'WU Inbound - Recepción de Remesas'
WHEN MTH."From_Msisdn" = '51942861841' OR MTH."To_Msisdn" = '51942861841' THEN 'SA - Dispersiones'
WHEN MTH."From_Msisdn" = '51936220933' OR MTH."To_Msisdn" = '51936220933' THEN 'SA - Comisiones'
WHEN MTH."From_Msisdn" = '***********' OR MTH."To_Msisdn" = '***********' THEN 'SA - Devoluciones'
ELSE 'NULL'
END AS "Nombre de la Empresa",
CASE 
WHEN (MTH."To_LoginID" LIKE 'digiflow%' OR  MTH."From_LoginID" LIKE 'digiflow%') AND MTH."Remarks" LIKE '%_%' THEN SUBSTR(MTH."Remarks", INSTR(MTH."Remarks", '_') + 1, LENGTH(MTH."Remarks") - INSTR(MTH."Remarks", '_'))
ELSE '-'
END AS "Código de Producto",
CASE
WHEN  MTH."To_LoginID" LIKE 'lindley%' OR  MTH."From_LoginID" LIKE 'lindley%' THEN 'Azulito'
WHEN  MTH."To_LoginID" LIKE 'airtimebitel%' OR  MTH."From_LoginID" LIKE 'airtimebitel%' THEN 'Bitel'
WHEN  MTH."To_LoginID" LIKE 'airtimeclaro%' OR  MTH."From_LoginID" LIKE 'airtimeclaro%' THEN 'Claro'
WHEN  MTH."To_LoginID" LIKE 'airtimemovistar%' OR  MTH."From_LoginID" LIKE 'airtimemovistar%' THEN 'Movistar'
WHEN  MTH."To_LoginID" LIKE 'airtimeentel%' OR  MTH."From_LoginID" LIKE 'airtimeentel%' THEN 'Entel'
ELSE '-'
END AS "Operador Destino",
"TransferID" AS ID,
TO_CHAR(MTH."TransferDate", 'YYYY-MM-DD HH24:MI:SS') AS "Fecha",
CASE WHEN "TransactionType" = 'DEPOSIT' then 'NULL' ELSE MTH."From_Msisdn" END AS "from_msisdn",
CASE WHEN "TransactionType" = 'DEPOSIT' then 'NULL' ELSE MTH."From_Profile" END AS "from_perfil",
CASE WHEN "TransactionType" = 'TRANSFER_TO_ANY_BANK_ACCOUNT' then 'NULL' ELSE MTH."To_Msisdn" END AS "to_msisdn",
CASE WHEN "TransactionType" = 'TRANSFER_TO_ANY_BANK_ACCOUNT' then 'NULL' ELSE MTH."To_Profile" END AS "to_perfil",
CASE WHEN "To_LoginID" LIKE '%lindley%' 
OR "To_LoginID" LIKE '%airtimebitel%' 
OR "To_LoginID" LIKE '%airtimeentel%' 
OR "To_LoginID" LIKE '%airtimeclaro%' 
OR "To_LoginID" LIKE '%airtimemovistar%' 
OR "To_LoginID" LIKE '%unique%' 
OR "To_LoginID" LIKE '%bitel%'
OR "To_LoginID" LIKE '%claro%'
THEN MTH."Remarks"
ELSE '-'
END AS "Número / DNI / Código del beneficiario",
LPAD(SUBSTR(TO_CHAR(MTH."Amount"), 1, LENGTH(TO_CHAR(MTH."Amount"))-2), LENGTH(TO_CHAR(MTH."Amount"))-2, '0') || '.' || SUBSTR(TO_CHAR(MTH."Amount"), LENGTH(TO_CHAR(MTH."Amount"))-1, 2) AS "Monto",
MTH."TransactionType" AS "transaction_type",
MTH."Context" as "context",
'COMMITTED'  AS "transaction_status",
0 AS "to_fee",
CASE WHEN MTH."TransactionType" IN ('REVERSAL','REFUND') THEN MTH."Comment" ELSE '' END AS "comment"
FROM TRX_MOD MTH
WHERE ("From_Msisdn" IN ('***********', '***********', '***********', '***********', '***********', '***********', '51999000003', '51999000004', '51999000005','51982836806', '51924798776', '51989320150', '51946594070', '51989102431', '51982768506','51942861841','51936220933') 
OR "To_Msisdn" IN ('***********', '***********', '***********', '***********', '***********', '***********', '51999000003', '51999000004', '51999000005','51982836806', '51924798776', '51989320150', '51946594070', '51989102431', '51982768506','51942861841','51936220933'))

Conexión cerrada.
